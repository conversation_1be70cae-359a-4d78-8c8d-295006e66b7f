/**
 ******************************************************************************
 * @file           : main.c
 * @brief          : Main program body
 ******************************************************************************
 * @attention
 *
 * Copyright (c) 2023 STMicroelectronics.
 * All rights reserved.
 *
 * This software is licensed under terms that can be found in the LICENSE file
 * in the root directory of this software component.
 * If no LICENSE file comes with this software, it is provided AS-IS.
 *
 ******************************************************************************
 */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include <stdbool.h>

#include "stm32h7xx_hal.h"
#include "bootloader_node.h"
#include "bootloader_cfg.h"
#include "bootloader_image.h"
#include "app_gpio.h"
#include "app_crc.h"
#include "app_can.h"
#include "app_watchdog.h"
#include "no_init.h"

extern int bootloader_main(void);
extern void PeriphCommonClock_Config(void);
extern void SystemClock_Config(void);

static inline void start_application(void)
{
  GPIO_DeInit();
  CRC_DeInit();

  uint32_t *application = (uint32_t*)APPLICATION_ADDRESS;

  __set_CONTROL(0);
  __set_MSP(application[0]);

  void (*reset_handler)(void) = (void(*)(void))application[1];

  reset_handler();

  for(;;);
}

/**
 * @brief  The application entry point.
 * @retval int
 */
int main(void)
{
  uint32_t magic;
  GPIO_Init();
  magic = (GPIO_ReadInput(enLOCAL_IN__DIP_SW_01) == 1) ?
                BOOTLOADER_HOLD_MAGIC : 0;
  // If not initialized by GPIO, check application magic
  if(magic == 0)
  {
    magic = boot_get_magic();
  }

  //Check bootloader magic value
  if(magic == BOOTLOADER_HOLD_MAGIC)
  {
    // Go into bootloader value
    bootloader_main();
  }
  else
  {
    // Verify if application is valid
    CRC_Init();
    bool valid_app = Bootloader_ValidateApplication(
        SYSTEM_NODE,
        APPLICATION_HEADER_ADDRESS,
        APPLICATION_ADDRESS);
    if(valid_app == true)
    {
      // Jump to application
      start_application();
    }
    else
    {
      // Go into bootloader main if application is invalid
      bootloader_main();
    }
  }

  // SHOULD NEVER HIT THIS POINT
  for(;;);
}

void EXTI15_10_IRQHandler(void)
{
  if(EXTI->PR1 & GPIO_PIN_10)
  {

  }
}

/**
 * @brief  This function is executed in case of error occurrence.
 * @retval None
 */
void Error_Handler(void)
{
   /* USER CODE BEGIN Error_Handler_Debug */
   /* User can add his own implementation to report the HAL error return state */
   __disable_irq();

   while(1)
   {

   }
   /* USER CODE END Error_Handler_Debug */
}
