/* USER CODE BEGIN Header */
/**
 ******************************************************************************
 * @file   fatfs.c
 * @brief  Code for fatfs applications
 ******************************************************************************
 * @attention
 *
 * Copyright (c) 2023 STMicroelectronics.
 * All rights reserved.
 *
 * This software is licensed under terms that can be found in the LICENSE file
 * in the root directory of this software component.
 * If no LICENSE file comes with this software, it is provided AS-IS.
 *
 ******************************************************************************
 */
/* USER CODE END Header */
#include "fatfs.h"

uint8_t retUSBH; /* Return value for USBH */
char USBHPath[4]; /* USBH logical drive path */
FATFS USBHFatFS; /* File system object for USBH logical drive */
FIL USBHFile; /* File object for USBH */

/* USER CODE BEGIN Variables */

/* USER CODE END Variables */

void MX_FATFS_Init(void) {
   /*## FatFS: Link the USBH driver ###########################*/
   retUSBH = FATFS_LinkDriver(&USBH_Driver, USBHPath);

   /* USER CODE BEGIN Init */
   /* additional user code for init */
   /* USER CODE END Init */
}

/**
 * @brief  Gets Time from RTC
 * @param  None
 * @retval Time in DWORD
 */
DWORD get_fattime(void) {
   /* USER CODE BEGIN get_fattime */
   return 0;
   /* USER CODE END get_fattime */
}

/* USER CODE BEGIN Application */
void MX_FATFS_DeInit(void) {
   /*## FatFS: Link the USBH driver ###########################*/
   retUSBH = FATFS_UnLinkDriver(USBHPath);

   /* USER CODE BEGIN Init */
   /* additional user code for init */
   /* USER CODE END Init */
}
/* USER CODE END Application */
