/*******************************************************************************
 * @Copyright (C) 2022 by Vantage Elevation
 * @file           : sr_LED.c
 * @version 		  : 1.0.0
 * @brief          : LED subroutine
 * @details		  : LED subroutine
 ********************************************************************************/

/*******************************************************************************
 * Includes
 ********************************************************************************/
#include "app.h"
#include "bootloader_def.h"
#include "sr_bootloader.h"
/*******************************************************************************
 * Function Prototypes
 ********************************************************************************/
static void
Init (void);
static void
Run (void);

/*******************************************************************************
 * Preprocessor Constants
 ********************************************************************************/
#define SUBROUTINE_FIRST_RUN_DELAY_1MS			0
#define SUBROUTINE_RUN_INTERVAL_1MS				100

/*******************************************************************************
 * Preprocessor Macros
 ********************************************************************************/

/*******************************************************************************
 * Typedefs
 ********************************************************************************/

/*******************************************************************************
 * Global Variable Definitions
 ********************************************************************************/
st_subroutine gstSR_LED = { .pfnInit = Init, .pfnRun = Run, .subroutine_name = "gstSR_LED"};

/*******************************************************************************
 * Variable Definitions
 ********************************************************************************/

/*******************************************************************************
 * Function Definitions
 ********************************************************************************/
/**
 * @fn  LED_UpdateHeartbeat(void)
 * @brief Updates LED heartbeat output
 * @param None
 * @return None
 */
static void LED_UpdateHeartbeat (void) {
   static uint8_t ucCounter_100ms;
//   en_bootloader_state eState = Bootloader_GetState ();
   uint8_t ucToggleRate_100ms = BOOTLOADER_LED_HEARTBEAT_OTHER_RATE_100MS;
//   if (eState <= BOOTLOADER_STATE__WAIT_FOR_TIMEOUT) {
//      ucToggleRate_100ms = BOOTLOADER_LED_HEARTBEAT_WAIT_FOR_TIMEOUT_100MS;
//   } else if ((eState >= BOOTLOADER_STATE__START_OF_ACTIVE)
//         && (eState <= BOOTLOADER_STATE__END_OF_ACTIVE)) {
//      ucToggleRate_100ms = BOOTLOADER_LED_HEARTBEAT_UPDATING_RATE_100MS;
//   }

   if (++ucCounter_100ms >= ucToggleRate_100ms) {
      uint8_t bLastLED = GPIO_ReadOutputCommand (enLOCAL_OUT__LED_HB);
      GPIO_WriteOutput (enLOCAL_OUT__LED_HB, !bLastLED);
      ucCounter_100ms = 0;
   }
}
/**
 * @fn  LED_UpdateFault(void)
 * @brief Updates LED fault output
 * @param None
 * @return None
 */
static void LED_UpdateFault (void) {
//   static uint8_t ucCounter_100ms;
//   bootloader_error_t eError = BOOTLOADER_ERROR__NONE;
//   if (eError == BOOTLOADER_ERROR__NONE) {
//      GPIO_WriteOutput (enLOCAL_OUT__LED_FLT, INACTIVE);
//   } else if ((eError >= BOOTLOADER_ERROR__INVALID_APP_PRIMARY_INFO)
//         && (eError <= BOOTLOADER_ERROR__INVALID_APP_SECONDARY_CRC)) {
//      if (++ucCounter_100ms >= BOOTLOADER_LED_FAULT_INVALID_APP_RATE_100MS) {
//         uint8_t bLastLED = GPIO_ReadOutputCommand (enLOCAL_OUT__LED_FLT);
//         GPIO_WriteOutput (enLOCAL_OUT__LED_FLT, !bLastLED);
//         ucCounter_100ms = 0;
//      }
//   } else if ((eError >= BOOTLOADER_ERROR__FLASH_ERASE_FAILURE)
//         && (eError <= BOOTLOADER_ERROR__INVALID_CORE)) {
//      if (++ucCounter_100ms >= BOOTLOADER_LED_FAULT_UPDATE_FAILED_RATE_100MS) {
//         uint8_t bLastLED = GPIO_ReadOutputCommand (enLOCAL_OUT__LED_FLT);
//         GPIO_WriteOutput (enLOCAL_OUT__LED_FLT, !bLastLED);
//         ucCounter_100ms = 0;
//      }
//   } else {
//      if (++ucCounter_100ms >= BOOTLOADER_LED_FAULT_UPDATE_OTHER_100MS) {
//         uint8_t bLastLED = GPIO_ReadOutputCommand (enLOCAL_OUT__LED_FLT);
//         GPIO_WriteOutput (enLOCAL_OUT__LED_FLT, !bLastLED);
//         ucCounter_100ms = 0;
//      }
//   }
}
/**
 * @fn  Init(void)
 * @brief Initializes LED subroutine first run delay, run interval, and variables
 * @param None
 * @return None
 */
static void Init (void) {
   gstSR_LED.uwFirstRunDelay_1ms = SUBROUTINE_FIRST_RUN_DELAY_1MS;
   gstSR_LED.uwRunInterval_1ms = SUBROUTINE_RUN_INTERVAL_1MS;
}
/**
 * @fn  Run(void)
 * @brief LED subroutine run function executed every uwRunInterval_1ms
 * @param None
 * @return None
 */
static void Run (void) {
   LED_UpdateHeartbeat ();
   LED_UpdateFault ();
}
