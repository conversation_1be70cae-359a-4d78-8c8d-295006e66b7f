/*******************************************************************************
 * @Copyright (C) 2025 by Vantage Elevation
 * @file           : uart_config.c
 * @version        : 1.0.1
 * @brief          : UART initialization and DMA configuration
 * @details        : Contains initialization routines for UART4 and UART7,
 *                   including DMA setup, NVIC configuration, and blocking/DMA
 *                   I/O wrappers for both UARTs. UART7 is used for BLE, while
 *                   UART4 supports general-purpose communication.
 *
 * @change-log     :
 *   07/01/2025, [1.0.0] : [*] Initial version with UART4 and UART7 peripheral,
 *                             DMA, GPIO, NVIC configuration, and basic I/O APIs.
 *   07/11/2025, [1.0.1] : [~] Added Doxygen-style function header comments for
 *                             all UART-related functions.
 ******************************************************************************/

#include "app_uart.h"

UART_HandleTypeDef UART4_Handle;
UART_HandleTypeDef UART7_Handle;

DMA_HandleTypeDef DMA_UART7_RXHandle;
DMA_HandleTypeDef DMA_UART7_TXHandle;

/**
 * @brief Initializes UART7 peripheral for BLE communication with DMA support.
 *
 * @details Configures UART7 with predefined parameters including baud rate,
 *          word length, stop bits, parity, and oversampling. HAL_UART_Init()
 *          is used to apply settings. DMA and NVIC are configured separately
 *          in the MSP init function.
 *
 * @note DMA and NVIC setup is performed inside HAL_UART_MspInit().
 *
 * @return None. Calls Error_Handler() if initialization fails.
 */

void UART7_Init(void)
{

	/* USER CODE BEGIN UART7_Init 0 */

	/* USER CODE END UART7_Init 0 */

	/* USER CODE BEGIN UART7_Init 1 */

	/* USER CODE END UART7_Init 1 */
	UART7_Handle.Instance = UART7;
	UART7_Handle.Init.BaudRate = UART7_BAUDRATE;
	UART7_Handle.Init.WordLength = UART_WORDLENGTH_8B;
	UART7_Handle.Init.StopBits = UART_STOPBITS_1;
	UART7_Handle.Init.Parity = UART_PARITY_NONE;
	UART7_Handle.Init.Mode = UART_MODE_TX_RX;
	UART7_Handle.Init.HwFlowCtl = UART_HWCONTROL_NONE;
	UART7_Handle.Init.OverSampling = UART_OVERSAMPLING_16;
	if (HAL_UART_Init(&UART7_Handle) != HAL_OK)
	{
		Error_Handler();
	}

	/* USER CODE BEGIN UART7_Init 2 */

	/* USER CODE END UART7_Init 2 */

}

/**
 * @brief De-initializes UART7 and releases associated hardware resources.
 *
 * @details Calls the HAL MSP de-init routine to disable UART7 clock, DMA, GPIO,
 *          and interrupt settings.
 *
 * @return None.
 */
void UART7_DeInit(void)
{
	HAL_UART_MspDeInit(&UART7_Handle);
}

/**
 * @brief Initializes UART4 for general-purpose UART communication.
 *
 * @details Configures UART4 with parameters like baud rate, parity, stop bits,
 *          and prescaler. HAL_UART_Init() is called to apply the configuration.
 *
 * @note Ensure clocks and GPIOs are already configured before use.
 *
 * @return None. Calls Error_Handler() if initialization fails.
 */
void UART4_Init(void)
{
	UART4_Handle.Instance             = UART4;
	UART4_Handle.Init.BaudRate        = UART4_BAUDRATE;
	UART4_Handle.Init.WordLength      = UART_WORDLENGTH_8B;
	UART4_Handle.Init.StopBits        = UART_STOPBITS_1;
	UART4_Handle.Init.Parity          = UART_PARITY_NONE;
	UART4_Handle.Init.HwFlowCtl       = UART_HWCONTROL_NONE;
	UART4_Handle.Init.Mode            = UART_MODE_TX_RX;
	UART4_Handle.Init.ClockPrescaler  = UART_PRESCALER_DIV1;
	UART4_Handle.Init.OneBitSampling  = UART_ONE_BIT_SAMPLE_DISABLE;
	UART4_Handle.Init.OverSampling    = UART_OVERSAMPLING_16;

	if(HAL_UART_Init(&UART4_Handle) != HAL_OK)
	{
		Error_Handler();
	}
}

/**
 * @brief De-initializes UART4 and releases GPIO, clock, and interrupt lines.
 *
 * @details Calls HAL_UART_MspDeInit() to clean up all resources used by UART4.
 *
 * @return None.
 */
void UART4_DeInit(void)
{
	HAL_UART_MspDeInit(&UART4_Handle);
}

/**
 * @brief Transmits data over UART4 in blocking mode.
 *
 * @details Performs a synchronous UART transmission. The function blocks until
 *          data is sent or timeout expires.
 *
 * @param[in] data Pointer to data buffer to be sent.
 * @param[in] size Number of bytes to transmit.
 *
 * @return None.
 */
void UART4_write(const uint8_t *data, uint32_t size)
{
	HAL_UART_Transmit(&UART4_Handle, data, size, UART4_WRITE_TIMEOUT);
}

/**
 * @brief Receives data over UART4 in blocking mode.
 *
 * @details Performs a synchronous UART reception. The function blocks until
 *          all requested bytes are received or timeout expires.
 *
 * @param[out] data Pointer to buffer to store received data.
 * @param[in]  size Number of bytes to receive.
 *
 * @return None.
 */
void UART4_read(uint8_t * data, uint32_t size)
{
	HAL_UART_Receive(&UART4_Handle, data, size, UART4_READ_TIMEOUT);
}

/**
 * @brief Initializes GPIO, clock, DMA, and NVIC for UART peripherals.
 *
 * @details This function is called by HAL during UART initialization. It sets
 *          up GPIOs, enables peripheral clocks, configures DMA for UART7, and
 *          sets NVIC priorities.
 *
 * @param[in] huart Pointer to UART handle (UART4 or UART7).
 *
 * @return None.
 */
void HAL_UART_MspInit(UART_HandleTypeDef *huart)
{
	GPIO_InitTypeDef GPIO_InitStruct = {0};
	if (huart->Instance==UART4)
	{
		/* Enable GPIO clock*/
		__HAL_RCC_GPIOH_CLK_ENABLE();

		/* Enable clock for UART */
		__HAL_RCC_UART4_CLK_ENABLE();

	    /* UART4 clock enable */
	    __HAL_RCC_UART4_CLK_ENABLE();

	    __HAL_RCC_GPIOH_CLK_ENABLE();
	    /**UART4 GPIO Configuration
	    PH13     ------> UART4_TX
	    PH14     ------> UART4_RX
	    */
	    GPIO_InitStruct.Pin = GPIO_PIN_13|GPIO_PIN_14;
	    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
	    GPIO_InitStruct.Pull = GPIO_NOPULL;
	    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
	    GPIO_InitStruct.Alternate = GPIO_AF8_UART4;
	    HAL_GPIO_Init(GPIOH, &GPIO_InitStruct);
	}
	else if(huart->Instance==UART7)
	{

		/* UART7 clock enable */
		__HAL_RCC_UART7_CLK_ENABLE();

		__HAL_RCC_GPIOE_CLK_ENABLE();
		/**UART7 GPIO Configuration
		PE7     ------> UART7_RX
		PE8     ------> UART7_TX
		 */
		GPIO_InitStruct.Pin = GPIO_PIN_7|GPIO_PIN_8;
		GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
		GPIO_InitStruct.Pull = GPIO_NOPULL;
		GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
		GPIO_InitStruct.Alternate = GPIO_AF7_UART7;
		HAL_GPIO_Init(GPIOE, &GPIO_InitStruct);

		/* UART7 DMA Init */
		/* UART7_RX Init */
		DMA_UART7_RXHandle.Instance = DMA1_Stream0;
		DMA_UART7_RXHandle.Init.Request = DMA_REQUEST_UART7_RX;
		DMA_UART7_RXHandle.Init.Direction = DMA_PERIPH_TO_MEMORY;
		DMA_UART7_RXHandle.Init.PeriphInc = DMA_PINC_DISABLE;
		DMA_UART7_RXHandle.Init.MemInc = DMA_MINC_ENABLE;
		DMA_UART7_RXHandle.Init.PeriphDataAlignment = DMA_PDATAALIGN_BYTE;
		DMA_UART7_RXHandle.Init.MemDataAlignment = DMA_MDATAALIGN_BYTE;
		DMA_UART7_RXHandle.Init.Mode = DMA_NORMAL;
		DMA_UART7_RXHandle.Init.Priority = DMA_PRIORITY_VERY_HIGH;
		DMA_UART7_RXHandle.Init.FIFOMode = DMA_FIFOMODE_DISABLE;
		if (HAL_DMA_Init(&DMA_UART7_RXHandle) != HAL_OK)
		{
			Error_Handler();
		}

		__HAL_LINKDMA(huart,hdmarx,DMA_UART7_RXHandle);

		/* UART7_TX Init */
		DMA_UART7_TXHandle.Instance = DMA1_Stream1;
		DMA_UART7_TXHandle.Init.Request = DMA_REQUEST_UART7_TX;
		DMA_UART7_TXHandle.Init.Direction = DMA_MEMORY_TO_PERIPH;
		DMA_UART7_TXHandle.Init.PeriphInc = DMA_PINC_DISABLE;
		DMA_UART7_TXHandle.Init.MemInc = DMA_MINC_ENABLE;
		DMA_UART7_TXHandle.Init.PeriphDataAlignment = DMA_PDATAALIGN_BYTE;
		DMA_UART7_TXHandle.Init.MemDataAlignment = DMA_MDATAALIGN_BYTE;
		DMA_UART7_TXHandle.Init.Mode = DMA_NORMAL;
		DMA_UART7_TXHandle.Init.Priority = DMA_PRIORITY_VERY_HIGH;
		DMA_UART7_TXHandle.Init.FIFOMode = DMA_FIFOMODE_DISABLE;
		if (HAL_DMA_Init(&DMA_UART7_TXHandle) != HAL_OK)
		{
			Error_Handler();
		}

		__HAL_LINKDMA(huart,hdmatx,DMA_UART7_TXHandle);

		/* UART7 interrupt Init */
		HAL_NVIC_SetPriority(UART7_IRQn, 0, 0);
		HAL_NVIC_EnableIRQ(UART7_IRQn);
		/* USER CODE BEGIN UART7_MspInit 1 */

		/* USER CODE END UART7_MspInit 1 */
	}
}

/**
 * @brief De-initializes GPIO, clock, DMA, and NVIC for UART peripherals.
 *
 * @details This function disables resources used by UART4 or UART7, including
 *          DMA channels, interrupts, and GPIOs.
 *
 * @param[in] huart Pointer to UART handle (UART4 or UART7).
 *
 * @return None.
 */
void HAL_UART_MspDeInit(UART_HandleTypeDef *huart)
{

	if (huart->Instance==UART4)
	{
		/* USER CODE BEGIN UART4_MspDeInit 0 */

		/* USER CODE END UART4_MspDeInit 0 */
		/* Peripheral clock disable */
		__HAL_RCC_UART4_CLK_DISABLE();

		/**UART4 GPIO Configuration
		PH13     ------> UART4_TX
		PH14     ------> UART4_RX
		 */
		HAL_GPIO_DeInit(GPIOH, GPIO_PIN_13|GPIO_PIN_14);

		/* USER CODE BEGIN UART4_MspDeInit 1 */

		/* USER CODE END UART4_MspDeInit 1 */
	}
	else if(huart->Instance==UART7)
	{
		/* USER CODE BEGIN UART7_MspDeInit 0 */

		/* USER CODE END UART7_MspDeInit 0 */
		/* Peripheral clock disable */
		__HAL_RCC_UART7_CLK_DISABLE();

		/**UART7 GPIO Configuration
		PE7     ------> UART7_RX
		PE8     ------> UART7_TX
		 */
		HAL_GPIO_DeInit(GPIOE, GPIO_PIN_7|GPIO_PIN_8);

		/* UART7 DMA DeInit */
		HAL_DMA_DeInit(huart->hdmarx);
		HAL_DMA_DeInit(huart->hdmatx);

		/* UART7 interrupt Deinit */
		HAL_NVIC_DisableIRQ(UART7_IRQn);
		/* USER CODE BEGIN UART7_MspDeInit 1 */

		/* USER CODE END UART7_MspDeInit 1 */
	}
}

/**
 * @brief Enables DMA1 clock and configures stream interrupts.
 *
 * @details DMA1_Stream0 is used for UART7 RX, and DMA1_Stream1 is used for TX.
 *          Interrupts are configured with default priority.
 *
 * @return None.
 */
void DMA_Init(void)
{

	/* DMA controller clock enable */
	__HAL_RCC_DMA1_CLK_ENABLE();

	/* DMA interrupt init */
	/* DMA1_Stream0_IRQn interrupt configuration */
	HAL_NVIC_SetPriority(DMA1_Stream0_IRQn, 0, 0);
	HAL_NVIC_EnableIRQ(DMA1_Stream0_IRQn);
	/* DMA1_Stream1_IRQn interrupt configuration */
	HAL_NVIC_SetPriority(DMA1_Stream1_IRQn, 0, 0);
	HAL_NVIC_EnableIRQ(DMA1_Stream1_IRQn);

}

/**
 * @brief Transmits data over UART7 using DMA.
 *
 * @details Starts a non-blocking DMA transfer using HAL_UART_Transmit_DMA().
 *
 * @param[in] data Pointer to buffer to transmit.
 * @param[in] size Number of bytes to send.
 *
 * @return None.
 */
void UART7_write_DMA(uint8_t *data, uint32_t size)
{
    HAL_UART_Transmit_DMA(&UART7_Handle, data, size);
}

/**
 * @brief Receives data over UART7 using DMA.
 *
 * @details Starts a non-blocking DMA receive operation for a fixed number of
 *          bytes using HAL_UART_Receive_DMA().
 *
 * @param[out] data Buffer to store received data.
 * @param[in]  size Number of bytes to receive.
 *
 * @return None.
 */
void UART7_read_DMA(uint8_t *data, uint32_t size)
{
    HAL_UART_Receive_DMA(&UART7_Handle, data, size);
}

/**
 * @brief Starts UART7 DMA reception with idle line detection.
 *
 * @details Enables variable-length reception by triggering completion when the
 *          UART line goes idle. Uses HAL_UARTEx_ReceiveToIdle_DMA().
 *
 * @param[out] data Buffer to store received data.
 * @param[in]  size Maximum number of bytes expected.
 *
 * @return None.
 */
void UART7_read_Idle_DMA(uint8_t *data, uint32_t size)
{
    HAL_UARTEx_ReceiveToIdle_DMA(&UART7_Handle, data, size);
}

/**
 * @brief Callback for UART transmit complete event.
 *
 * @details Called by HAL when DMA-based transmission completes on UART7.
 *
 * @param[in] huart UART handle that triggered the callback.
 *
 * @return None.
 */
void HAL_UART_TxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == UART7)
    {
        // Transmission complete
    }
}

/**
 * @brief Callback for UART DMA receive with idle detection.
 *
 * @details Called when UART7 completes a ReceiveToIdle DMA operation. Size
 *          parameter indicates the number of bytes received.
 *
 * @param[in] huart UART handle that triggered the callback.
 * @param[in] Size  Number of bytes received before idle was detected.
 *
 * @return None.
 */
void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
    if (huart->Instance == UART7)
    {
        // DMA ReceiveToIdle complete
        // Use 'Size' to know how many bytes were received
    }
}

