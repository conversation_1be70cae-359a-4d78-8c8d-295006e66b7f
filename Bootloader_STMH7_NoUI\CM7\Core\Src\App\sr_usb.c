/*******************************************************************************
 * @Copyright (C) 2022 by Vantage Elevation
 * @file         : sr_usb.c
 * @version 		  : 1.0.0
 * @brief        : USB subroutine
 * @details		  : USB subroutine
 ********************************************************************************/
/*******************************************************************************
 * Includes
 ********************************************************************************/
#include "sr_usb.h"
#include "sr_bootloader.h"
#include "bootloader_usb.h"

#include "fatfs.h"
#include "ff.h"
#include "usb_host.h"
#include "app_usb.h"

/*******************************************************************************
 * Function Prototypes
 ********************************************************************************/
static void Init(void);
static void Run(void);

/*******************************************************************************
 * Preprocessor Constants
 ********************************************************************************/
#define SUBROUTINE_FIRST_RUN_DELAY_1MS		(0U)
#define SUBROUTINE_RUN_INTERVAL_1MS				(10U)

/*******************************************************************************
 * Preprocessor Macros
 ********************************************************************************/

/*******************************************************************************
 * Typedefs
 ********************************************************************************/

/*******************************************************************************
 * Global Variable Definitions
 ********************************************************************************/
st_subroutine gstSR_USB =
{
		.pfnInit = Init,
		.pfnRun = Run,
		.subroutine_name = "gstSR_USB"
};

/*******************************************************************************
 * Variable Definitions
 ********************************************************************************/
//ApplicationTypeDef USBState;

/**
 * @fn  void USB_ConnectionStateCallback(uint8_t ucConnectionState)
 * @brief User callback invoked by MX_USB_HOST_Process
 * @param ucConnectionState defined by ApplicationTypeDef
 * @return None
 */
void USB_ConnectionStateCallback(uint8_t ucConnectionState)
{
//	USBState = ucConnectionState;
	bootloader->usb.state = (usb_state_t)ucConnectionState;

	switch(ucConnectionState) {
	  case APPLICATION_IDLE:
		 break;
	  case APPLICATION_START:
		 break;
	  case APPLICATION_READY:
		 USB_MountDrive();
		 // Read app files
     bootloader->app_files.count = Bootloader_USBReadFilesInfo("*.bin",
         &bootloader->app_files.names[0],
         &bootloader->app_files.sizes[0],
         USB_APP_FILES_MAX);
		 // Read cfg files
     bootloader->cfg_files.count = Bootloader_USBReadFilesInfo("*.cfg",
         &bootloader->cfg_files.names[0],
         &bootloader->cfg_files.sizes[0],
         USB_CFG_FILES_MAX);
		 break;
	  case APPLICATION_DISCONNECT:
		 USB_UnmountDrive();
		 bootloader->app_files.count = 0;
		 bootloader->cfg_files.count = 0;
		 break;
	  default:
		 break;
	}
}

/**
 * @fn  Init(void)
 * @brief Initializes USB subroutine first run delay, run interval, and variables
 * @param None
 * @return None
 */
static void Init(void)
{
   gstSR_USB.uwFirstRunDelay_1ms = SUBROUTINE_FIRST_RUN_DELAY_1MS;
   gstSR_USB.uwRunInterval_1ms = SUBROUTINE_RUN_INTERVAL_1MS;
}

/**
 * @fn  Run(void)
 * @brief USB subroutine run function executed every uwRunInterval_1ms
 * @param None
 * @return None
 */
static void Run(void)
{
  MX_USB_HOST_Process();

  // TODO: If number of USB files changed, read names and size again

	if(bootloader->usb.state == USB_READY)
	{
		static event_t const event = { USB_READY_SIG };
		queue_post(&bootloader->event_queue, (event_t*)&event);
	}
}

