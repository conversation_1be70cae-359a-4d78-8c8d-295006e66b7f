/****************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file sr_parameters.c
 * @version 1.0.0
* @brief Subroutine updating processor parameter CRCs, and services requests between nodes for parameters
* @details Subroutine updating processor parameter CRCs, and services requests between nodes for parameters
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include "app.h"
#include <string.h>
#if 0
#include "lcefs_def.h"
#include "lce_fs.h"
#include "lcefs_map.h"
#include "fs/fs.h"
#endif
/******************************************************************************
* Function Prototypes
*******************************************************************************/
static void Init(void);
static void Run(void);

/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/
#define SUBROUTINE_FIRST_RUN_DELAY_1MS                            (0)
#define SUBROUTINE_RUN_INTERVAL_1MS                               (10)

#ifdef ENABLE_PARAMETER_MASTER
#define MASTER_RECENT_EDIT_TIMEOUT_1MS    (3000) /* Request queue is processed when full or when there has been an unprocessed element for this timeout */
#endif
/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/


/******************************************************************************
* Module Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variable Definitions
*******************************************************************************/
st_subroutine gstSR_Parameters =
{
   .pfnInit = Init,
   .pfnRun = Run,
   .subroutine_name = "gstSR_Parameters"
};
/******************************************************************************
* Module Variable Definitions
*******************************************************************************/
#ifdef ENABLE_PARAMETER_MASTER
static en_param_blocks eBlockToUpdate_OEM = NUM_PARAM_BLOCKS;
static en_param_blocks eBlockToUpdate_User = NUM_PARAM_BLOCKS;
#endif
#ifdef ENABLE_EFLASH_NODE
typedef enum
{
   EFLASH_UPDATE_STATE__INACTIVE,
   EFLASH_UPDATE_STATE__UPDATE_USER_COPY,

   NUM_EFLASH_UPDATE_STATES
}en_eflash_update_state;
/* Used to split the update of the recovery sector and the write from recovery to user copy */
static en_eflash_update_state eEFlashUpdateState;
#endif
/******************************************************************************
* Function Definitions
*******************************************************************************/
#ifdef ENABLE_EFLASH_NODE
/* @brief Regularly checks for differences between the eflash and running copy of parameters and if detected, updates the eflash
 * Adapted from LceFs_Commit  */
static void UpdateParametersEFlash(void)
{
   static uint8_t ucBlockToUpdate;
   uint32_t uiCRC_EFlash;
   uint32_t uiCRC_Local;
   uint8_t ucBlockIndex;
   en_pass_fail eError = PASS;
   en_active_inactive bWriteToEFlash;
#if defined(ENABLE_PARAMETER_MASTER)
   /* Suppress update of eflash when default is in progress */
   if( ( eBlockToUpdate_OEM >= NUM_PARAM_BLOCKS )
    && ( eBlockToUpdate_User >= NUM_PARAM_BLOCKS )
    && ( Param_GetState() == PARAM_STATE__IDLE )
    && ( Motion_GetMotionRunFlag(MOTION_RUN_FLAG__RUNNING) == INACTIVE )
    && ( Motion_GetMotionRunFlag(MOTION_RUN_FLAG__SAFETY_TOGGLE) == INACTIVE ) )
    {
#elif defined(ENABLE_EFLASH_NODE) /* CT_A7 */
   /* Suppress this process when out of sync with master to prevent repeated writes to the EFLASH */
   if( ( Param_GetBlockToUpdate() == NUM_PARAM_BLOCKS )
    && ( Motion_GetMotionRunFlag(MOTION_RUN_FLAG__RUNNING) == INACTIVE )
    && ( Motion_GetMotionRunFlag(MOTION_RUN_FLAG__SAFETY_TOGGLE) == INACTIVE ) )
   {
#endif
      switch(eEFlashUpdateState)
      {
         case EFLASH_UPDATE_STATE__INACTIVE:
         {
#ifdef ENABLE_PARAMETER_MASTER
            int32_t *piLastEditTime_1ms = Param_GetRecentMasterEditTime_1ms();
            int32_t iDiff_1ms = Timer_GetCount_1ms() - *piLastEditTime_1ms;
            /* Suppress update of eflash when default is in progress */
            if( iDiff_1ms >= MASTER_RECENT_EDIT_TIMEOUT_1MS )
            {
#endif
               bWriteToEFlash = INACTIVE;
               for(ucBlockIndex = 0; ucBlockIndex < NUM_PARAM_BLOCKS; ucBlockIndex++)
               {
                  uiCRC_EFlash = Param_GetBlockCRC_EFlash(ucBlockIndex);
                  uiCRC_Local = Param_GetBlockCRC_Local(ucBlockIndex);
                  if( uiCRC_Local != uiCRC_EFlash )
                  {
                     bWriteToEFlash = ACTIVE;
                     ucBlockToUpdate = ucBlockIndex;
                     break;
                  }
               }
               if( bWriteToEFlash == ACTIVE )
               {
                   eEFlashUpdateState = EFLASH_UPDATE_STATE__UPDATE_USER_COPY;
               }
               else
               {
#ifdef ENABLE_PARAMETER_MASTER
                  *piLastEditTime_1ms = Timer_GetCount_1ms() - MASTER_RECENT_EDIT_TIMEOUT_1MS;
               }
            }
#else
         }
#endif
      }
            break;
         case EFLASH_UPDATE_STATE__UPDATE_USER_COPY:
         {
//            int32_t iStartTime_1us = Timer_GetCount_1us();
            st_param_block *pstParamBlocks = Param_GetBlockStructure(ucBlockToUpdate);
            uint32_t uiAddressToWrite = FRAM_START_ADDRESS__PARAMETERS_USER + (ucBlockToUpdate * EFLASH_PARAM_BLOCK_SIZE__BYTES);
            for(uint8_t i = 0; i < FRAM_WRITES_TYPE__TRIPLET; i++)
            {
                eError |= FRAM_WriteSingleBlock(uiAddressToWrite, pstParamBlocks, EFLASH_PARAM_BLOCK_SIZE__BYTES);
                uiAddressToWrite += FRAM_PARAMETERS_USER_SIZE__BYTES;
            }
//            int32_t iDiffTime_1us = Timer_GetCount_1us() - iStartTime_1us; // 9,809 at 80mhz
            if( eError == PASS )
            {
               Param_SetBlockCRC_EFlash(ucBlockToUpdate, Param_GetBlockCRC_Local(ucBlockToUpdate));
               eEFlashUpdateState = EFLASH_UPDATE_STATE__INACTIVE;
            }
         }
            break;
         default:
            eEFlashUpdateState = EFLASH_UPDATE_STATE__INACTIVE;
            break;
      }
   }
   if( eError != PASS )
   {
      en_car_alarm eAlarm = ( System_GetNodeID() == SYS_NODE__MR_A7 ) ? CALM__DEFAULT_BACKUP_FAILED_MR : CALM__DEFAULT_BACKUP_FAILED_CT;
      Alarms_SetAlarm(eAlarm);
   }
   else if( eEFlashUpdateState != EFLASH_UPDATE_STATE__INACTIVE )
   {
      en_car_alarm eAlarm = ( System_GetNodeID() == SYS_NODE__MR_A7 ) ? CALM__EFLASH_UPDATE_MR : CALM__EFLASH_UPDATE_CT;
      Alarms_SetAlarm(eAlarm);
   }
}
#endif
#ifdef ENABLE_PARAMETER_MASTER
/* @brief Serves default requests from other nodes */
static void CheckForDefaultRequest(void)
{
#if 0//AS todo revisit
   LceFs_ParamValue stParamBlock;
   st_param_block_details stBlockDetails;
   uint32_t uiValue;
   uint16_t uwParamIndex;
   en_param_default_type *peRequestDefault_OEM = Param_GetRequestDefault_OEM();
   en_param_default_type *peRequestDefault_User = Param_GetRequestDefault_UserBackup();
   lce_fs_error_code eError = LCE_FS_OK;
   if( ( *peRequestDefault_OEM < NUM_PARAM_DEFAULT_TYPE ) && ( eBlockToUpdate_OEM >= NUM_PARAM_BLOCKS ) )
   {
      eBlockToUpdate_OEM = 0;
   }
   else if( ( *peRequestDefault_User < NUM_PARAM_DEFAULT_TYPE ) && ( eBlockToUpdate_User >= NUM_PARAM_BLOCKS ) )
   {
      eBlockToUpdate_User = 0;
   }

   if( ( Motion_GetMotionRunFlag(MOTION_RUN_FLAG__RUNNING) == INACTIVE )
    && ( Motion_GetMotionRunFlag(MOTION_RUN_FLAG__SAFETY_TOGGLE) == INACTIVE ) )
   {
      if( eBlockToUpdate_OEM < NUM_PARAM_BLOCKS )
      {
         eError = LceFs_ReadParamBlock(Param_GetFileSystemHandlingStructure(), LCE_FS_PARAM_SECTOR_CONFIG_DEFAULT_PARAM, &stParamBlock, eBlockToUpdate_OEM, true);
         if( eError == LCE_FS_OK )
         {
            Param_GetBlockDetails(eBlockToUpdate_OEM, &stBlockDetails);
            if( stBlockDetails.eType < NUM_PARAM_TYPES )
            {
               for(uwParamIndex = stBlockDetails.uwParamIndex_Start; uwParamIndex <= stBlockDetails.uwParamIndex_End; uwParamIndex++)
               {
                  if( Param_CheckDefaultType(stBlockDetails.eType, uwParamIndex, *peRequestDefault_OEM) == ACTIVE )
                  {
                     // taken from LceMap_ReadParam
                     switch(stBlockDetails.eType)
                     {
                        case LCE_FS_TYPE_1BIT:
                           uiValue = LceFsParamValue_ReadValue1Bit(uwParamIndex, &stParamBlock);
                           break;
                        case LCE_FS_TYPE_8BIT:
                           uiValue = LceFsParamValue_ReadValue8Bit(uwParamIndex, &stParamBlock);
                           break;
                        case LCE_FS_TYPE_16BIT:
                           uiValue = LceFsParamValue_ReadValue16Bit(uwParamIndex, &stParamBlock);
                           break;
                        case LCE_FS_TYPE_24BIT:
                           uiValue = LceFsParamValue_ReadValue24Bit(uwParamIndex, &stParamBlock);
                           break;
                        case LCE_FS_TYPE_32BIT:
                           uiValue = LceFsParamValue_ReadValue32Bit(uwParamIndex, &stParamBlock);
                           break;
                        default: uiValue = 0; break;
                     }
                     Param_WriteValue_ByType(stBlockDetails.eType, uwParamIndex, uiValue);
                  }
               }
               Param_UpdateLocalCRC(eBlockToUpdate_OEM); /* CRC calculation suppressed during default. Force manual recalculation after completion of a block */
            }
            if(++eBlockToUpdate_OEM >= NUM_PARAM_BLOCKS)
            {
               *peRequestDefault_OEM = NUM_PARAM_DEFAULT_TYPE; // Clear the request after all blocks have been processed
            }
         }
         Alarms_SetAlarm(CALM__DEFAULTING);
      }
      else if( eBlockToUpdate_User < NUM_PARAM_BLOCKS )
      {
         eError = LceFs_ReadParamBlock(Param_GetFileSystemHandlingStructure(), LCE_FS_PARAM_SECTOR_USER_DEFAULT_PARAM, &stParamBlock, eBlockToUpdate_User, true);
         if( eError == LCE_FS_OK )
         {
            Param_GetBlockDetails(eBlockToUpdate_User, &stBlockDetails);
            if( stBlockDetails.eType < NUM_PARAM_TYPES )
            {
               for(uwParamIndex = stBlockDetails.uwParamIndex_Start; uwParamIndex <= stBlockDetails.uwParamIndex_End; uwParamIndex++)
               {
                  if( Param_CheckDefaultType(stBlockDetails.eType, uwParamIndex, *peRequestDefault_User) == ACTIVE )
                  {
                     // taken from LceMap_ReadParam
                     switch(stBlockDetails.eType)
                     {
                        case LCE_FS_TYPE_1BIT:
                           uiValue = LceFsParamValue_ReadValue1Bit(uwParamIndex, &stParamBlock);
                           break;
                        case LCE_FS_TYPE_8BIT:
                           uiValue = LceFsParamValue_ReadValue8Bit(uwParamIndex, &stParamBlock);
                           break;
                        case LCE_FS_TYPE_16BIT:
                           uiValue = LceFsParamValue_ReadValue16Bit(uwParamIndex, &stParamBlock);
                           break;
                        case LCE_FS_TYPE_24BIT:
                           uiValue = LceFsParamValue_ReadValue24Bit(uwParamIndex, &stParamBlock);
                           break;
                        case LCE_FS_TYPE_32BIT:
                           uiValue = LceFsParamValue_ReadValue32Bit(uwParamIndex, &stParamBlock);
                           break;
                        default: uiValue = 0; break;
                     }
                     Param_WriteValue_ByType(stBlockDetails.eType, uwParamIndex, uiValue);
                  }
               }
               Param_UpdateLocalCRC(eBlockToUpdate_User); /* CRC calculation suppressed during default. Force manual recalculation after completion of a block */
            }
            if(++eBlockToUpdate_User >= NUM_PARAM_BLOCKS)
            {
               *peRequestDefault_User = NUM_PARAM_DEFAULT_TYPE; // Clear the request after all blocks have been processed
            }
         }
         Alarms_SetAlarm(CALM__DEFAULTING);
      }
      if( eError != LCE_FS_OK )
      {
         en_car_alarm eAlarm = ( System_GetNodeID() == SYS_NODE__MR_B7 ) ? CALM__DEFAULT_BACKUP_FAILED_MR : CALM__DEFAULT_BACKUP_FAILED_CT;
         Alarms_SetAlarm(eAlarm);
      }
   }
#endif
}
#endif
#ifdef ENABLE_EFLASH_NODE
/* @brief Serves backup parameters requests from other nodes */
static void CheckForBackupRequest(void)
{
#if 0//AS todo revisit
   /* Only update if the parameters are synchronized with the master */
   if( ( Param_GetBlockToUpdate() == NUM_PARAM_BLOCKS )
    && ( Motion_GetMotionRunFlag(MOTION_RUN_FLAG__RUNNING) == INACTIVE )
    && ( Motion_GetMotionRunFlag(MOTION_RUN_FLAG__SAFETY_TOGGLE) == INACTIVE ) )
   {
      uint8_t *bRequestUserBackup = Param_GetRequestUserBackupUpdate();
      if( *bRequestUserBackup == ACTIVE )
      {
         LceFS_Handler_t *plceFs = Param_GetFileSystemHandlingStructure();
         lce_fs_error_code eError = LceFs_TransferParamValue(plceFs, LCE_FS_PARAM_SECTOR_USER_PARAM, LCE_FS_PARAM_SECTOR_USER_DEFAULT_PARAM, LCE_FS_TRPV_F_CHECK_WRITE);  // 55 ms
         if( eError != LCE_FS_OK )
         {
            en_car_alarm eAlarm = ( System_GetNodeID() == SYS_NODE__MR_B7 ) ? CALM__DEFAULT_BACKUP_FAILED_MR : CALM__DEFAULT_BACKUP_FAILED_CT;
            Alarms_SetAlarm(eAlarm);
         }
         else *bRequestUserBackup = INACTIVE;
         en_car_alarm eAlarm = ( System_GetNodeID() == SYS_NODE__MR_B7 ) ? CALM__BACKING_UP_MR : CALM__BACKING_UP_CT;
         Alarms_SetAlarm(eAlarm);
      }
   }
#endif
}
#endif

/* @fn static void Init(void)
 * @brief Initialization function for the subroutine's run interval, first run delay, and state machine variables
 * @param None
 * @return None
 */
static void Init(void)
{
   gstSR_Parameters.uwFirstRunDelay_1ms = SUBROUTINE_FIRST_RUN_DELAY_1MS;
   gstSR_Parameters.uwRunInterval_1ms = SUBROUTINE_RUN_INTERVAL_1MS;
}

/* @fn static void Run(void)
 * @brief Run function for the subroutine that is executed every uwRunInterval_1ms
 * @param None
 * @return None
 */
static void Run(void)
{
   Param_Run();

#ifdef ENABLE_PARAMETER_MASTER
   CheckForDefaultRequest();
#endif
#ifdef ENABLE_EFLASH_NODE
   UpdateParametersEFlash();
   CheckForBackupRequest();
#endif
}
