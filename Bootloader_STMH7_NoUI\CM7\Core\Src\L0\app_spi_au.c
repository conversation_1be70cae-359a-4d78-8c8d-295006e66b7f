#include "app_spi_au.h"
//#include "lwrb/lwrb.h"
#include "main.h"

//SPI_HandleTypeDef hspi5;
SPI_HandleTypeDef hspi6;

//DMA_HandleTypeDef hbdma0;

uint8_t SPI_AU_RX_buffer[SPI_AU_RX_BUFFER_SIZE];
//uint8_t SPI_AU_TX_buffer[SPI_AU_TX_BUFFER_SIZE] __attribute__((section(".spi6_tx_data")));

SPI_device_t SPI_AU_TX =
{
  .hspi   = &hspi6,
//  .hdma   = &hbdma0,
//  .buffer = SPI_AU_TX_buffer,
  .size   = SPI_AU_TX_BUFFER_SIZE
};

SPI_device_t SPI_AU_RX =
{
//  .hspi   = &hspi5,
//  .hdma   = 0,
//  .buffer = SPI_AU_RX_buffer,
//  .size   = SPI_AU_RX_BUFFER_SIZE
};

void SPI_AU_RX_Init(SPI_device_t *dev);
void SPI_AU_TX_Init(SPI_device_t *dev);

void SPI_AU_Init(void)
{
  SPI_AU_TX_Init(&SPI_AU_TX);
  SPI_AU_RX_Init(&SPI_AU_RX);
}

/**SPI6 GPIO Configuration
 PG8     ------> SPI6_NSS
 PG13     ------> SPI6_SCK
 PG14     ------> SPI6_MOSI
 */
void SPI_AU_TX_Init(SPI_device_t *dev)
{
//  lwrb_init(&dev->ringbuffer,
//      &dev->buffer[0],
//      dev->size);

  dev->tx_state      = SPI_TX_IDLE;
  dev->transmit_size = 0;

  /* SPI6 clock enable */
  __HAL_RCC_SPI6_CLK_ENABLE();
  __HAL_RCC_GPIOG_CLK_ENABLE();
  __HAL_RCC_BDMA_CLK_ENABLE();

  GPIO_InitTypeDef GPIO_InitStruct = { 0 };
  GPIO_InitStruct.Pin       = GPIO_PIN_8 | GPIO_PIN_13 | GPIO_PIN_14;
  GPIO_InitStruct.Mode      = GPIO_MODE_AF_PP;
  GPIO_InitStruct.Pull      = GPIO_NOPULL;
  GPIO_InitStruct.Speed     = GPIO_SPEED_FREQ_MEDIUM;
  GPIO_InitStruct.Alternate = GPIO_AF5_SPI6;
  HAL_GPIO_Init(GPIOG, &GPIO_InitStruct);

//  hbdma0.Instance                 = BDMA_Channel0;
//  hbdma0.Init.Request             = BDMA_REQUEST_SPI6_TX;
//  hbdma0.Init.Direction           = DMA_MEMORY_TO_PERIPH;
//  hbdma0.Init.PeriphInc           = DMA_PINC_DISABLE;
//  hbdma0.Init.MemInc              = DMA_MINC_ENABLE;
//  hbdma0.Init.PeriphDataAlignment = DMA_PDATAALIGN_BYTE;
//  hbdma0.Init.MemDataAlignment    = DMA_MDATAALIGN_BYTE;
//  hbdma0.Init.Mode                = DMA_NORMAL;
//  hbdma0.Init.Priority            = DMA_PRIORITY_LOW;
//  hbdma0.Init.FIFOMode            = DMA_FIFOMODE_DISABLE;
//  hbdma0.Init.MemBurst            = DMA_MBURST_SINGLE;
//  hbdma0.Init.PeriphBurst         = DMA_PBURST_SINGLE;
//  hbdma0.Parent                   = &hspi6;
//  if(HAL_DMA_Init(&hbdma0) != HAL_OK)
//  {
//    Error_Handler();
//  }

  hspi6.Instance                        = SPI6;
  hspi6.Init.Mode                       = SPI_MODE_MASTER;
  hspi6.Init.Direction                  = SPI_DIRECTION_2LINES_TXONLY;
  hspi6.Init.DataSize                   = SPI_DATASIZE_16BIT;
  hspi6.Init.CLKPolarity                = SPI_POLARITY_LOW;
  hspi6.Init.CLKPhase                   = SPI_PHASE_1EDGE;
  hspi6.Init.NSS                        = SPI_NSS_HARD_OUTPUT;
  hspi6.Init.BaudRatePrescaler          = SPI_BAUDRATEPRESCALER_16;// PLL3Q = 16 MHZ / 16 = 1 MHZ
  hspi6.Init.FirstBit                   = SPI_FIRSTBIT_MSB;
  hspi6.Init.TIMode                     = SPI_TIMODE_DISABLE;
  hspi6.Init.CRCCalculation             = SPI_CRCCALCULATION_DISABLE;
  hspi6.Init.CRCPolynomial              = 0x0;
  hspi6.Init.NSSPMode                   = SPI_NSS_PULSE_DISABLE;
  hspi6.Init.NSSPolarity                = SPI_NSS_POLARITY_LOW;
  hspi6.Init.FifoThreshold              = SPI_FIFO_THRESHOLD_01DATA;
  hspi6.Init.TxCRCInitializationPattern = SPI_CRC_INITIALIZATION_ALL_ZERO_PATTERN;
  hspi6.Init.RxCRCInitializationPattern = SPI_CRC_INITIALIZATION_ALL_ZERO_PATTERN;
  hspi6.Init.MasterSSIdleness           = SPI_MASTER_SS_IDLENESS_00CYCLE;
  hspi6.Init.MasterInterDataIdleness    = SPI_MASTER_INTERDATA_IDLENESS_00CYCLE;
  hspi6.Init.MasterReceiverAutoSusp     = SPI_MASTER_RX_AUTOSUSP_DISABLE;
  hspi6.Init.MasterKeepIOState          = SPI_MASTER_KEEP_IO_STATE_ENABLE;
  hspi6.Init.IOSwap                     = SPI_IO_SWAP_DISABLE;
//  hspi6.hdmatx                          = &hbdma0;
  if(HAL_SPI_Init(&hspi6) != HAL_OK)
  {
     Error_Handler();
  }

//  HAL_NVIC_SetPriority(SPI6_IRQn, 3, 0);
//  HAL_NVIC_EnableIRQ(SPI6_IRQn);
//
//  HAL_NVIC_SetPriority(BDMA_Channel0_IRQn, 3, 0);
//  HAL_NVIC_EnableIRQ(BDMA_Channel0_IRQn);
}

/**SPI5 GPIO Configuration
 PJ10     ------> SPI5_MOSI
 PK0     ------> SPI5_SCK
 PK1     ------> SPI5_NSS
 */
void SPI_AU_RX_Init(SPI_device_t *dev)
{
//  lwrb_init(&dev->ringbuffer,
//      &dev->buffer[0],
//      dev->size);
//
//  __HAL_RCC_SPI5_CLK_ENABLE();
//  __HAL_RCC_GPIOJ_CLK_ENABLE();
//  __HAL_RCC_GPIOK_CLK_ENABLE();
//
//  GPIO_InitTypeDef GPIO_InitStruct = { 0 };
//  GPIO_InitStruct.Pin       = GPIO_PIN_10;
//  GPIO_InitStruct.Mode      = GPIO_MODE_AF_PP;
//  GPIO_InitStruct.Pull      = GPIO_NOPULL;
//  GPIO_InitStruct.Speed     = GPIO_SPEED_FREQ_VERY_HIGH;
//  GPIO_InitStruct.Alternate = GPIO_AF5_SPI5;
//  HAL_GPIO_Init(GPIOJ, &GPIO_InitStruct);
//
//  GPIO_InitStruct.Pin       = GPIO_PIN_0 | GPIO_PIN_1;
//  GPIO_InitStruct.Mode      = GPIO_MODE_AF_PP;
//  GPIO_InitStruct.Pull      = GPIO_NOPULL;
//  GPIO_InitStruct.Speed     = GPIO_SPEED_FREQ_VERY_HIGH;
//  GPIO_InitStruct.Alternate = GPIO_AF5_SPI5;
//  HAL_GPIO_Init(GPIOK, &GPIO_InitStruct);
//
//  hspi5.Instance                        = SPI5;
//  hspi5.Init.Mode                       = SPI_MODE_SLAVE;
//  hspi5.Init.Direction                  = SPI_DIRECTION_2LINES_RXONLY;
//  hspi5.Init.DataSize                   = SPI_DATASIZE_16BIT;
//  hspi5.Init.CLKPolarity                = SPI_POLARITY_LOW;
//  hspi5.Init.CLKPhase                   = SPI_PHASE_1EDGE;
//  hspi5.Init.NSS                        = SPI_NSS_HARD_INPUT;
//  hspi5.Init.FirstBit                   = SPI_FIRSTBIT_MSB;
//  hspi5.Init.TIMode                     = SPI_TIMODE_DISABLE;
//  hspi5.Init.CRCCalculation             = SPI_CRCCALCULATION_DISABLE;
//  hspi5.Init.CRCPolynomial              = 0x0;
//  hspi5.Init.NSSPMode                   = SPI_NSS_PULSE_DISABLE;
//  hspi5.Init.NSSPolarity                = SPI_NSS_POLARITY_LOW;
//  hspi5.Init.FifoThreshold              = SPI_FIFO_THRESHOLD_01DATA;
//  hspi5.Init.TxCRCInitializationPattern = SPI_CRC_INITIALIZATION_ALL_ZERO_PATTERN;
//  hspi5.Init.RxCRCInitializationPattern = SPI_CRC_INITIALIZATION_ALL_ZERO_PATTERN;
//  hspi5.Init.MasterSSIdleness           = SPI_MASTER_SS_IDLENESS_00CYCLE;
//  hspi5.Init.MasterInterDataIdleness    = SPI_MASTER_INTERDATA_IDLENESS_00CYCLE;
//  hspi5.Init.MasterReceiverAutoSusp     = SPI_MASTER_RX_AUTOSUSP_DISABLE;
//  hspi5.Init.MasterKeepIOState          = SPI_MASTER_KEEP_IO_STATE_DISABLE;
//  hspi5.Init.IOSwap                     = SPI_IO_SWAP_DISABLE;
//  if(HAL_SPI_Init(&hspi5) != HAL_OK)
//  {
//     Error_Handler();
//  }
//
//  /* Initiate Receive only spi that will perpetually unload bytes to a ring buffer. Adapted from HAL_SPI_Receive_IT */
////   __HAL_SPI_DISABLE(&hspi5);
//   /* Set the number of data at current transfer */
////   MODIFY_REG(hspi5.Instance->CR2, SPI_CR2_TSIZE, SPI_RX_TRANSFER_SIZE__WORD);
//   /* Enable SPI peripheral */
////   __HAL_SPI_ENABLE(&hspi5);
//   /* Enable RXP, OVR, FRE, MODF interrupts */
////   __HAL_SPI_ENABLE_IT(&hspi5, (SPI_IT_RXP | SPI_IT_OVR | SPI_IT_FRE | SPI_IT_MODF));
//
//  /* SPI5 interrupt Init */
//  HAL_NVIC_SetPriority(SPI5_IRQn, 1, 0);
//  HAL_NVIC_EnableIRQ(SPI5_IRQn);
}

void SPI_AU_Transmit(uint16_t *data, uint16_t size)
{
  HAL_SPI_Transmit(SPI_AU_TX.hspi,
      (const uint8_t*)&data[0],
      size,
      HAL_MAX_DELAY);
}

void SPI_AU_TransmitEncoded(uint16_t *data, uint16_t size)
{
  /* Load data into ringbuffer */
  /* TODO: if data written to ringbuffer is not equal to the size
   * request, then we have a tx overflow
   */
  uint16_t c = 0xFE00;
  HAL_SPI_Transmit(SPI_AU_TX.hspi, (const uint8_t*)&c, 1, HAL_MAX_DELAY);

  for(uint32_t i = 0; i < size; i++)
  {
    uint16_t c = data[i];
    switch(c)
    {
    case 0xFE00:
    case 0xEF00:
    case 0xEE00:
    {
      uint16_t esc_seq[2] = {0xEE00, ~c};
      HAL_SPI_Transmit(SPI_AU_TX.hspi, (const uint8_t*)&esc_seq[0], 2, HAL_MAX_DELAY);
    }
      break;
    default:
      HAL_SPI_Transmit(SPI_AU_TX.hspi, (const uint8_t*)&c, 1, HAL_MAX_DELAY);
      break;
    }
  }

  c = 0xEF00;
  HAL_SPI_Transmit(SPI_AU_TX.hspi, (const uint8_t*)&c, 1, HAL_MAX_DELAY);

//  HAL_SPI_Transmit(SPI_AU_TX.hspi, (const uint8_t*)data, size, HAL_MAX_DELAY);

//  if(lwrb_write(&SPI_AU_TX.ringbuffer, data, size) == size)
//  {
//    if(SPI_AU_TX.tx_state == SPI_TX_IDLE)
//    {
//      /* Grab a chunk of data from the ringbuffer and
//       * start a new transmission*/
//      SPI_AU_TX.transmit_size = lwrb_get_linear_block_read_length(&SPI_AU_TX.ringbuffer) / 2;
//      uint8_t *data = lwrb_get_linear_block_read_address(&SPI_AU_TX.ringbuffer);
//
//      SPI_AU_TX.tx_state = SPI_TX_TRANSFER_COMPLETE;
//      HAL_SPI_Transmit_DMA(SPI_AU_TX.hspi, data, SPI_AU_TX.transmit_size);
//    }
//  }
//  else
//  {
//    // Buffer overflow
//  }
}

//void SPI6_IRQHandler(void)
//{
//  HAL_SPI_IRQHandler(&hspi6);
//}

//void SPI5_IRQHandler(void)
//{
//
//}

//void BDMA_Channel0_IRQHandler(void)
//{
//  HAL_DMA_IRQHandler(&hbdma0);
//}

