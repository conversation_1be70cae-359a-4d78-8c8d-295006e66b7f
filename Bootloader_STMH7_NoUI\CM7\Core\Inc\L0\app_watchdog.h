/*******************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file           : app_watchdog.h
 * @version         : 1.0.0
 * @brief Functions for initializing the watchdog
 * @details Functions for initializing the watchdog
 *****************************************************************************/
#ifndef _WATCHDOG_H_
#define _WATCHDOG_H_

/******************************************************************************
 * Includes
 *******************************************************************************/
#include <stdint.h>

/******************************************************************************
 * Preprocessor Constants
 *******************************************************************************/

/******************************************************************************
 * Configuration Constants
 *******************************************************************************/

/******************************************************************************
 * Macros
 *******************************************************************************/

/******************************************************************************
 * Typedefs
 *******************************************************************************/

/******************************************************************************
 * Global Variables
 *******************************************************************************/

/******************************************************************************
 * Function Prototypes
 *******************************************************************************/
void Watchdog_Init(void);
void Watchdog_SetTimeout(uint32_t timeout);
void Watchdog_Feed(void);

#endif /* _WATCHDOG_H_ */
