/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_local_outputs.c
* @version 		  : 1.0.0
* @brief          : local outputs subroutine
* @details		  : local outputs subroutine
********************************************************************************/

/*******************************************************************************
* Includes
********************************************************************************/
#include "app.h"

/*******************************************************************************
* Function Prototypes
********************************************************************************/
static void Init(void);
static void Run(void);

/*******************************************************************************
* Preprocessor Constants
********************************************************************************/
#define SUBROUTINE_FIRST_RUN_DELAY_1MS			100
#define SUBROUTINE_RUN_INTERVAL_1MS				10
/*******************************************************************************
* Preprocessor Macros
********************************************************************************/
#define LOCAL_INPUT_DEBOUNCE_TIME__1MS			50

/*******************************************************************************
* Typedefs
********************************************************************************/


/*******************************************************************************
* Global Variable Definitions
********************************************************************************/
st_subroutine gstSR_Local_Outputs =
{
	.pfnInit = Init,
	.pfnRun = Run,
	.subroutine_name = "gstSR_Local_Outputs"
};

/*******************************************************************************
* Variable Definitions
********************************************************************************/
static uint32_t auiOutputBitmap[BITMAP32_ARRAY_SIZE(NUM_LOCAL_OUTPUTS)];

/*******************************************************************************
* Function Definitions
********************************************************************************/
/**
  * @fn  uint32_t LocalOutputs_GetOutputBitmap(uint8_t ucArrayIndex)
  * @brief Returns the status of outputs in bitmap form
  * @param ucArrayIndex, array index
  * @return Returns the status of outputs in bitmap form
  */
uint32_t LocalOutputs_GetOutputBitmap(uint8_t ucArrayIndex)
{
	return auiOutputBitmap[ucArrayIndex];
}
/**
  * @fn  uint8_t LocalOutputs_GetOutputValue(en_local_outputs eOutput)
  * @brief Returns the status of a particular output
  * @param eOutput, the local output index
  * @return Returns 1 if the output is active, 0 otherwise
  */
uint8_t LocalOutputs_GetOutputValue(en_local_outputs eOutput)
{
	return System_GetBitByIndex(&auiOutputBitmap[0], eOutput);
}
/**
  * @fn  static void UpdateOutputPins(void)
  * @brief Takes the local output commands and updates the corresponding pin states
  * @param None
  * @return None
  */
static void UpdateOutputPins(void)
{
	/* Update hardware pin states based on commands */
	for(en_local_outputs eOutput = enLOCAL_OUT__OUTPUT_01; eOutput <= enLOCAL_OUT__OUTPUT_12; eOutput++)
	{
	   uint8_t ucBitIndex = eOutput - enLOCAL_OUT__OUTPUT_01;
	  uint8_t bActive = System_GetBitByIndex(&auiOutputBitmap[0], ucBitIndex);
	   GPIO_WriteOutput(eOutput, bActive);
	}
}
/**
  * @fn  Init(void)
  * @brief Initializes local outputs subroutine first run delay, run interval, and variables
  * @param None
  * @return None
  */
static void Init(void)
{
	gstSR_Local_Outputs.uwFirstRunDelay_1ms = SUBROUTINE_FIRST_RUN_DELAY_1MS;
	gstSR_Local_Outputs.uwRunInterval_1ms = SUBROUTINE_RUN_INTERVAL_1MS;
}
/**
  * @fn  Run(void)
  * @brief local outputs subroutine run function executed every uwRunInterval_1ms
  * @param None
  * @return None
  */
static void Run(void)
{
//	OutputMapping_GetBoardTerminalOutputs_MR((uint8_t *) auiOutputBitmap);
	UpdateOutputPins();
}
