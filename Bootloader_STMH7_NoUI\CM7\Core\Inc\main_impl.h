/*******************************************************************************
 * @Copyright (C) 2022 by Vantage Elevation
 * @file           : main_impl.h
 * @version 		  : 1.0.0
 * @brief          : Access functions for node specific implementation of main program body
 * @details		  : Access functions for node specific implementation of main program body
 ********************************************************************************/
#ifndef _MAIN_IMPL_H_
#define _MAIN_IMPL_H_
/*******************************************************************************
 * Includes
 ********************************************************************************/
#include <stdint.h>

/*******************************************************************************
 * Preprocessor Constants
 ********************************************************************************/

/*******************************************************************************
 * Configuration Constants
 ********************************************************************************/

/*******************************************************************************
 * Macros
 ********************************************************************************/

/*******************************************************************************
 * Typedefs
 ********************************************************************************/

/*******************************************************************************
 * Global Variables
 ********************************************************************************/

/*******************************************************************************
 * Function Prototypes
 ********************************************************************************/
int MainImpl(void);

// Node specific implementation should be defined in the main_impl_*.c file
void ExitToApplicationImpl(void);

#endif /* _MAIN_IMPL_H_ */
