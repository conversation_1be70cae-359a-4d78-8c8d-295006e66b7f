/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_Watchdog.c
* @version 		  : 1.0.0
* @brief          : Watchdog subroutine
* @details		  : Watchdog subroutine
********************************************************************************/

/*******************************************************************************
* Includes
********************************************************************************/
#include "app.h"

/*******************************************************************************
* Function Prototypes
********************************************************************************/
static void Init(void);
static void Run(void);

/*******************************************************************************
* Preprocessor Constants
********************************************************************************/
#define SUBROUTINE_FIRST_RUN_DELAY_1MS			0
#define SUBROUTINE_RUN_INTERVAL_1MS				500

/*******************************************************************************
* Preprocessor Macros
********************************************************************************/


/*******************************************************************************
* Typedefs
********************************************************************************/


/*******************************************************************************
* Global Variable Definitions
********************************************************************************/
st_subroutine gstSR_Watchdog =
{
	.pfnInit = Init,
	.pfnRun = Run,
	.subroutine_name = "gstSR_Watchdog"
};

/*******************************************************************************
* Variable Definitions
********************************************************************************/


/*******************************************************************************
* Function Definitions
********************************************************************************/
/**
  * @fn  Init(void)
  * @brief Initializes Watchodg subroutine first run delay, run interval, and variables
  * @param None
  * @return None
  */
static void Init(void)
{
	gstSR_Watchdog.uwFirstRunDelay_1ms = SUBROUTINE_FIRST_RUN_DELAY_1MS;
	gstSR_Watchdog.uwRunInterval_1ms = SUBROUTINE_RUN_INTERVAL_1MS;

	Watchdog_Init();
}
/**
  * @fn  Run(void)
  * @brief Watchdog subroutine run function executed every uwRunInterval_1ms
  * @param None
  * @return None
  */
static void Run(void)
{
	Watchdog_Feed();
}
