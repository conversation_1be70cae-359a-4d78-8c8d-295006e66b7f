/*******************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file           : sr_car_net.c
 * @version 		  : 1.0.0
 * @brief This subroutine executes load functions at the scheduled interval and transmits datagrams at the scheduled interval
 * @details This subroutine executes load functions at the scheduled interval and transmits datagrams at the scheduled interval
 *****************************************************************************/

/******************************************************************************
 * Includes
 *******************************************************************************/
#include "sr_car_net.h"

#include <string.h>

#include "network.h"
#include "system.h"
#include "app_can.h"
#include "sr_SPI_AB.h"
#include "sr_SPI_AU.h"
#include "sr_CAN1.h"
#include "bootloader_cfg.h"
#include "sr_bootloader.h"
#include "boot_dg_car_mr_a7.h"
#include "boot_dg_car_ct_a7.h"
#include "app_error.h"
#include "app_gpio.h"

/******************************************************************************
 * Function Prototypes
 *******************************************************************************/
static void Init(void);
static void Run(void);

/******************************************************************************
 * Module Preprocessor Constants
 *******************************************************************************/
#define SUBROUTINE_FIRST_RUN_DELAY_1MS                            (10U)
#define SUBROUTINE_RUN_INTERVAL_1MS                               (1U)
#define MAX_PACKETS_TRANSMIT_PER_CYCLE                            (5U)
/******************************************************************************
 * Module Preprocessor Macros
 *******************************************************************************/

/******************************************************************************
 * Module Typedefs
 *******************************************************************************/

/******************************************************************************
 * Global Variable Definitions
 *******************************************************************************/
st_subroutine gstSR_Car_Net =
{
    .pfnInit = Init,
    .pfnRun = Run,
    .subroutine_name = "gstSR_Car_Net"
};

static st_datagram_control *pstControl;
/******************************************************************************
 * Module Variable Definitions
 *******************************************************************************/

/******************************************************************************
 * Function Definitions
 *******************************************************************************/
#if IS_NODE(MR_A7_NODE)
void BOOT_DG_CAR_MR_A7_LoadData_Bootloader_Magic(void)
{
  un_datagram unData = { 0 };
  if(GPIO_ReadInput(enLOCAL_IN__DIP_SW_01) == 1)
  {
    unData.aui32[0] = BOOTLOADER_HOLD_MAGIC;
    bootloader->flags |= ARBITRATION;
  }
  else
  {
    unData.aui32[0] = 0x00000000;
    bootloader->flags &= ~ARBITRATION;
  }

  DGS_WriteDatagram(pstControl,
      BOOT_DG_CAR_MR_A7__Bootloader_Magic,
      (const uint8_t*)&unData.auc8[0]);
}

void BOOT_DG_CAR_MR_A7_LoadData_Bootloader_Status(void)
{
  un_datagram unData = { 0 };
  /* 1 byte - State */
  unData.auc8[0] = bootloader->state;
  /* 1 byte - Error */
  unData.auc8[1] = bootloader->error;
  /* 1 - byte flags */
  unData.auc8[2] = bootloader->flags;

  DGS_WriteDatagram(pstControl,
      BOOT_DG_CAR_MR_A7__Bootloader_Status,
      (const uint8_t*)&unData.auc8[0]);
}

void BOOT_DG_CAR_MR_A7_LoadData_Bootloader_LatchedReq(void)
{
  un_datagram unData = { 0 };

  unData.auc8[0] = bootloader->latched_request;
  memcpy(&unData.auc8[1],
      &bootloader->update.header,
      sizeof(app_header_t));

  DGS_WriteDatagram(pstControl,
      BOOT_DG_CAR_MR_A7__Bootloader_LatchedReq,
      (const uint8_t*)&unData.auc8[0]);
}

void BOOT_DG_CAR_MR_A7_LoadData_Bootloader_Progress(void)
{
  un_datagram unData = { 0 };
  uint32_t flash_progress;
  if(IS_REQ_LOCAL(SYSTEM_NODE, bootloader->latched_request))
  {
    flash_progress = bootloader->update.flash_written_cnt;
  }
  else
  {
    // We can only update the sent progress,
    // this does not accurately reflect how many
    // bytes the remote node has flashed
    flash_progress = bootloader->send.sent_cnt;
  }

  unData.auc8[0] = (flash_progress >> 24) & 0xFF;
  unData.auc8[1] = (flash_progress >> 16) & 0xFF;
  unData.auc8[2] = (flash_progress >> 8)  & 0xFF;
  unData.auc8[3] = (flash_progress >> 0)  & 0xFF;

  DGS_WriteDatagram(pstControl,
      BOOT_DG_CAR_MR_A7__Bootloader_Progress,
      (const uint8_t*)&unData.auc8[0]);
}

void BOOT_DG_CAR_MR_A7_LoadData_Bootloader_Local_App(void)
{
  // Local app image information
  app_header_t * header = (app_header_t*)APPLICATION_HEADER_ADDRESS;
  DGS_WriteDatagram(pstControl,
      BOOT_DG_CAR_MR_A7__Bootloader_Local_App,
      (const uint8_t *)header);
}

void BOOT_DG_CAR_MR_A7_LoadData_Bootloader_USB_Info(void)
{
  un_datagram unData = { 0 };
  /* 1 byte - USB state */
  unData.auc8[0] = bootloader->usb.state;
  /* 1 byte - USB error */
  unData.auc8[1] = bootloader->usb.error;
  /* 1 byte - USB app file count */
  unData.auc8[2] = bootloader->app_files.count;
  /* 1 byte - USB cfg file count */
  unData.auc8[3] = bootloader->cfg_files.count;

  DGS_WriteDatagram(pstControl,
      BOOT_DG_CAR_MR_A7__Bootloader_USB_Info,
      &unData.auc8[0]);
}

void BOOT_DG_CAR_MR_A7_LoadData_Bootloader_USB_AppFile(void)
{
  if(bootloader->app_files.count > 0)
  {
    un_datagram unData = { 0 };
    uint8_t index = bootloader->app_files.send_index;

    /* 1 byte - File index 0-MAX_FILES */
    unData.auc8[0] = index;

    /* 4 bytes - File size */
    uint32_t file_size = bootloader->app_files.sizes[index];
    unData.auc8[1] = (file_size >> 24) & 0xFF;
    unData.auc8[2] = (file_size >> 16) & 0xFF;
    unData.auc8[3] = (file_size >> 8)  & 0xFF;
    unData.auc8[4] = (file_size >> 0)  & 0xFF;

    /* 59 bytes - File name */
    uint32_t offset = index * USB_FILENAME_MAX_SIZE;
    char* filename = &bootloader->app_files.names[offset];
    uint32_t length = strlen(filename);
    if((length + 1) < USB_FILENAME_MAX_SIZE)
    {
      memcpy(&unData.ac8[5], &filename[0], length);
      unData.ac8[length + 5] = '\0';
    }
    else
    {
      // TODO: Just clamp the file to 59 bytes
      unData.ac8[5] = '\0';
    }

    DGS_WriteDatagram(pstControl,
        BOOT_DG_CAR_MR_A7__Bootloader_USB_AppFile,
        &unData.auc8[0]);

    bootloader->app_files.send_index = (index + 1) %
        bootloader->app_files.count;
  }
}

void BOOT_DG_CAR_MR_A7_LoadData_Bootloader_USB_CfgFile(void)
{
  if(bootloader->cfg_files.count > 0)
  {
    un_datagram unData = { 0 };
    uint8_t index = bootloader->cfg_files.send_index;

    /* 1 byte - File index 0-MAX_FILES */
    unData.auc8[0] = index;

    /* 4 bytes - File size */
    uint32_t file_size = bootloader->cfg_files.sizes[index];
    unData.auc8[1] = (file_size >> 24) & 0xFF;
    unData.auc8[2] = (file_size >> 16) & 0xFF;
    unData.auc8[3] = (file_size >> 8)  & 0xFF;
    unData.auc8[4] = (file_size >> 0)  & 0xFF;

    /* 59 bytes - File name */
    uint32_t offset = index * USB_FILENAME_MAX_SIZE;
    char* filename = &bootloader->cfg_files.names[offset];
    uint32_t length = strlen(filename);
    if((length + 1) < USB_FILENAME_MAX_SIZE)
    {
      memcpy(&unData.ac8[5], &filename[0], length);
      unData.ac8[length + 5] = '\0';
    }
    else
    {
      // TODO: Just clamp the file to 59 bytes
      unData.ac8[5] = '\0';
    }

    DGS_WriteDatagram(pstControl,
        BOOT_DG_CAR_MR_A7__Bootloader_USB_CfgFile,
        &unData.auc8[0]);

    bootloader->cfg_files.send_index = (index + 1) %
        bootloader->cfg_files.count;
  }
}

#elif IS_NODE(CT_A7_NODE)
void BOOT_DG_CAR_CT_A7_LoadData_Bootloader_Magic(void)
{
  un_datagram unData = { 0 };

  if(GPIO_ReadInput(enLOCAL_IN__DIP_SW_01) == 1)
  {
    unData.aui32[0] = BOOTLOADER_HOLD_MAGIC;
    bootloader->flags |= ARBITRATION;
  }
  else
  {
    unData.aui32[0] = 0x00000000;
    bootloader->flags &= ~ARBITRATION;
  }

  DGS_WriteDatagram(pstControl,
      BOOT_DG_CAR_CT_A7__Bootloader_Magic,
      (const uint8_t*)&unData.auc8[0]);
}

void BOOT_DG_CAR_CT_A7_LoadData_Bootloader_Status(void)
{
  un_datagram unData = { 0 };
  /* 1 byte - State */
  unData.auc8[0] = bootloader->state;
  /* 1 byte - Error */
  unData.auc8[1] = bootloader->error;
  /* 1 - byte flags */
  unData.auc8[2] = bootloader->flags;

  DGS_WriteDatagram(pstControl,
      BOOT_DG_CAR_CT_A7__Bootloader_Status,
      (const uint8_t*)&unData.auc8[0]);
}

void BOOT_DG_CAR_CT_A7_LoadData_Bootloader_LatchedReq(void)
{
  un_datagram unData = { 0 };

  unData.auc8[0] = bootloader->latched_request;
  memcpy(&unData.auc8[1],
      &bootloader->update.header,
      sizeof(app_header_t));

  DGS_WriteDatagram(pstControl,
      BOOT_DG_CAR_CT_A7__Bootloader_LatchedReq,
      (const uint8_t*)&unData.auc8[0]);
}

void BOOT_DG_CAR_CT_A7_LoadData_Bootloader_Progress(void)
{
  un_datagram unData = { 0 };
  uint32_t flash_progress;
  if(IS_REQ_LOCAL(SYSTEM_NODE, bootloader->latched_request))
  {
    flash_progress = bootloader->update.flash_written_cnt;
  }
  else
  {
    // We can only update the sent progress,
    // this does not accurately reflect how many
    // bytes the remote node has flashed
    flash_progress = bootloader->send.sent_cnt;
  }

  unData.auc8[0] = (flash_progress >> 24) & 0xFF;
  unData.auc8[1] = (flash_progress >> 16) & 0xFF;
  unData.auc8[2] = (flash_progress >> 8)  & 0xFF;
  unData.auc8[3] = (flash_progress >> 0)  & 0xFF;

  DGS_WriteDatagram(pstControl,
      BOOT_DG_CAR_CT_A7__Bootloader_Progress,
      (const uint8_t*)&unData.auc8[0]);
}

void BOOT_DG_CAR_CT_A7_LoadData_Bootloader_USB_Info(void)
{
  un_datagram unData = { 0 };
  /* 1 byte - USB state */
  unData.auc8[0] = bootloader->usb.state;
  /* 1 byte - USB error */
  unData.auc8[1] = bootloader->usb.error;
  /* 1 byte - USB app file count */
  unData.auc8[2] = bootloader->app_files.count;
  /* 1 byte - USB cfg file count */
  unData.auc8[3] = bootloader->cfg_files.count;

  DGS_WriteDatagram(pstControl,
      BOOT_DG_CAR_MR_A7__Bootloader_USB_Info,
      &unData.auc8[0]);
}

void BOOT_DG_CAR_CT_A7_LoadData_Bootloader_USB_AppFile(void)
{
  if(bootloader->app_files.count > 0)
  {
    un_datagram unData = { 0 };
    uint8_t index = bootloader->app_files.send_index;

    /* 1 byte - File index 0-MAX_FILES */
    unData.auc8[0] = index;

    /* 4 bytes - File size */
    uint32_t file_size = bootloader->app_files.sizes[index];
    unData.auc8[1] = (file_size >> 24) & 0xFF;
    unData.auc8[2] = (file_size >> 16) & 0xFF;
    unData.auc8[3] = (file_size >> 8)  & 0xFF;
    unData.auc8[4] = (file_size >> 0)  & 0xFF;

    /* 59 bytes - File name */
    uint32_t offset = index * USB_FILENAME_MAX_SIZE;
    char* filename = &bootloader->app_files.names[offset];
    uint32_t length = strlen(filename);
    if((length + 1) < USB_FILENAME_MAX_SIZE)
    {
      memcpy(&unData.ac8[5], &filename[0], length);
      unData.ac8[length + 5] = '\0';
    }
    else
    {
      // TODO: Just clamp the file to 59 bytes
      unData.ac8[5] = '\0';
    }

    DGS_WriteDatagram(pstControl,
        BOOT_DG_CAR_MR_A7__Bootloader_USB_AppFile,
        &unData.auc8[0]);

    bootloader->app_files.send_index = (index + 1) %
        bootloader->app_files.count;
  }
}

void BOOT_DG_CAR_CT_A7_LoadData_Bootloader_USB_CfgFile(void)
{
  if(bootloader->cfg_files.count > 0)
  {
    un_datagram unData = { 0 };
    uint8_t index = bootloader->cfg_files.send_index;

    /* 1 byte - File index 0-MAX_FILES */
    unData.auc8[0] = index;

    /* 4 bytes - File size */
    uint32_t file_size = bootloader->cfg_files.sizes[index];
    unData.auc8[1] = (file_size >> 24) & 0xFF;
    unData.auc8[2] = (file_size >> 16) & 0xFF;
    unData.auc8[3] = (file_size >> 8)  & 0xFF;
    unData.auc8[4] = (file_size >> 0)  & 0xFF;

    /* 59 bytes - File name */
    uint32_t offset = index * USB_FILENAME_MAX_SIZE;
    char* filename = &bootloader->cfg_files.names[offset];
    uint32_t length = strlen(filename);
    if((length + 1) < USB_FILENAME_MAX_SIZE)
    {
      memcpy(&unData.ac8[5], &filename[0], length);
      unData.ac8[length + 5] = '\0';
    }
    else
    {
      // TODO: Just clamp the file to 59 bytes
      unData.ac8[5] = '\0';
    }

    DGS_WriteDatagram(pstControl,
        BOOT_DG_CAR_MR_A7__Bootloader_USB_CfgFile,
        &unData.auc8[0]);

    bootloader->cfg_files.send_index = (index + 1) %
        bootloader->cfg_files.count;
  }
}

void BOOT_DG_CAR_CT_A7_LoadData_Bootloader_Local_App(void)
{
  app_header_t *header = (app_header_t*)APPLICATION_HEADER_ADDRESS;
  DGS_WriteDatagram(pstControl,
      BOOT_DG_CAR_CT_A7__Bootloader_Local_App,
      (const uint8_t*)header);
}
#endif

/* @fn static void TransmitHandler(void)
 * @brief Checks for datagrams to transmit
 * @param None
 * @return None
 */
static void TransmitHandler(void) {
//   st_datagram_control *pstControl = CarNet_GetDatagramControlStruct();
   st_CAN_msg stTxMsg;
   en_pass_fail eError;
   uint16_t uwDatagramIndex;
   st_datagram *pstDatagram;
   for(uint8_t i = 0; i < MAX_PACKETS_TRANSMIT_PER_CYCLE; i++)
   {
      if(DGS_GetNextDatagram(pstControl, &uwDatagramIndex) == PASS)
      {
         eError = PASS;
         pstDatagram = &pstControl->pastDatagrams[uwDatagramIndex];
         stTxMsg.bCAN_FD = ACTIVE;
         stTxMsg.bExtendedID = ACTIVE;
         stTxMsg.uiID = Network_GetPacketID(pstDatagram->ucSize_Bytes,
                                            uwDatagramIndex,
                                            SYS_NETWORK__CAR,
                                            System_GetNodeID());

         /* Path AB */
         if((Network_CheckDestination_Car(NET_FWD_PATH__AB, pstDatagram->uiDestinationBitmap) == PASS) &&
             (eError != FAIL))
         {
           stTxMsg.ucDLC = pstDatagram->ucSize_Bytes;
           memcpy(&stTxMsg.unData.auc8[0],
                  pstDatagram->paucData,
                  pstDatagram->ucSize_Bytes);
            SPI_AB_TransmitDatagram(&stTxMsg);
         }

         /* Path UI */
         if((Network_CheckDestination_Car(NET_FWD_PATH__UI, pstDatagram->uiDestinationBitmap) == PASS) &&
            (eError != FAIL))
         {
           st_SPI_msg stSPIRxMsg;
           stSPIRxMsg.size        = pstDatagram->ucSize_Bytes;
           stSPIRxMsg.datagram_id = (uint8_t)uwDatagramIndex;
           stSPIRxMsg.source_id   = System_GetNodeID();
           stSPIRxMsg.network_id  = SYS_NETWORK__CAR;
           stSPIRxMsg.data        = (uint16_t*)&pstDatagram->paucData[0];

           SPI_AU_TransmitDatagram(&stSPIRxMsg);

           /* Handle bootloader hold packets with old protocol */
           if((uwDatagramIndex == BOOT_DG_CAR_MR_A7__Bootloader_Magic))
           {
             stTxMsg.ucDLC = pstDatagram->ucSize_Bytes;
             memcpy(&stTxMsg.unData.auc8[0],
                    pstDatagram->paucData,
                    pstDatagram->ucSize_Bytes);
             SPI_AU_TransmitDatagram_v1(&stTxMsg);
           }
         }

         /* Path CAN to CT and COP */
         if(Network_CheckDestination_Car(NET_FWD_PATH__CAR, pstDatagram->uiDestinationBitmap) == PASS)
         {
           stTxMsg.ucDLC = pstDatagram->ucSize_Bytes;
           memcpy(&stTxMsg.unData.auc8[0],
                  pstDatagram->paucData,
                  pstDatagram->ucSize_Bytes);
           CAN1_TransmitDatagram(&stTxMsg);
         }

         /* Path to BLE */
         if(Network_CheckDestination_Car(NET_FWD_PATH__BLE, pstDatagram->uiDestinationBitmap) == PASS)
         {
           stTxMsg.ucDLC = pstDatagram->ucSize_Bytes;
           memcpy(&stTxMsg.unData.auc8[0],
                  &pstDatagram->paucData[0],
                  pstDatagram->ucSize_Bytes);
           // TODO: SoftDel please add this UART function
           // UART7_write_DMA(&stTxMsg.unData.auc8[0], pstDatagram->ucSize_Bytes);
         }

         /* If message has failed to send, remark it as the next packet due to send */
         if(eError == FAIL)
         {
            if(pstControl->uwLastSentDG)
            {
               pstControl->uwLastSentDG--;
            }
            else
            {
               pstControl->uwLastSentDG = pstControl->uwNumberOfDG - 1;
            }
            pstDatagram->bDataChanged = ACTIVE;
            break;
         }
         else
         {
            pstDatagram->ucPacketCounter++;
         }
      } else
      {
         break;
      }
   }
}

/* @fn static void Init(void)
 * @brief Initialization function for the subroutine's run interval, first run delay, and state machine variables
 *        Should be run before any run task's Run function
 * @param None
 * @return None
 */
static void Init(void) {
  gstSR_Car_Net.uwFirstRunDelay_1ms = SUBROUTINE_FIRST_RUN_DELAY_1MS;
  gstSR_Car_Net.uwRunInterval_1ms = SUBROUTINE_RUN_INTERVAL_1MS;

#if (SYSTEM_NODE == MR_A7_NODE)
  pstControl = BOOT_DG_CAR_MR_A7_GetDatagramControlStruct();
#elif (SYSTEM_NODE == CT_A7_NODE)
  pstControl = BOOT_DG_CAR_CT_A7_GetDatagramControlStruct();
#endif

  DGS_Init(pstControl);
}

/* @fn static void Run(void)
 * @brief Run function for the subroutine that is executed every uwRunInterval_1ms
 * @param None
 * @return None
 */
static void Run(void) {
   /* Load Datagram scheduler */
//   st_datagram_control *pstControl = CarNet_GetDatagramControlStruct();
   DGS_LoadHandler(pstControl);

   /* Transmit Datagram scheduler */
   TransmitHandler();
}
