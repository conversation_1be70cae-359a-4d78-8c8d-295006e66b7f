/******************************************************************************
 * @Copyright (C) 2025 by Vantage Elevation
 * @file           : sr_ble_uart.c
 * @version        : 1.0.2
 * @brief          : BLE UART subroutine
 * @brief Functions and definition for accessing and setting parameters of
 *        BootloaderView.
 * @details        : Handles UART7-based BLE packet reception using DMA,
 *                   CRC32 validation matching ESP32 implementation, and
 *                   command execution in subroutine-driven embedded flow.
 *                   Implements a single shared buffer for reception, and
 *                   maintains an empty Run() stub as per design.
 *
 * @change-log     :
 *   07/01/2025, [1.0.0] : [*] Initial version with BLE config read feature,
 *                         UART7 DMA-based reception, CRC32 validation
 *   07/11/2025, [1.0.1] : [+] Added BLE config write feature
 *   07/15/2025, [1.0.2] : [~] Improved error recovery logic with restarting Rx.
 ******************************************************************************/

/*******************************************************************************
 * Includes
 ******************************************************************************/
#include "app.h"
#include "fram_def.h"
#include "FRAM.h"
#include "system.h"

/*******************************************************************************
 * Function Prototypes
 ******************************************************************************/
static void Init(void);
static void Run(void);

/*******************************************************************************
 * Macros
 ******************************************************************************/
#define SUBROUTINE_FIRST_RUN_DELAY_1MS     (0U)
#define SUBROUTINE_RUN_INTERVAL_1MS        (100U)
#define CRC32_POLYNOMIAL                   (0x04C11DB7UL)
#define CRC32_INIT_VALUE                   (0xFFFFFFFFUL)

/*******************************************************************************
 * Static/Internal Buffers and State Variables
 ******************************************************************************/
static uint8_t uart_rx_header[UART_CMD_HEADER_SIZE];
static uint8_t uart_rx_crc[UART_CMD_CRC_SIZE];
static uint8_t *uart_rx_payload = USBReadBuffer;
static uint32_t uart_expected_payload_length = 0U;
static uint32_t uart_rx_offset = 0U;
static UART_RxState_t uart_rx_state = UART_RX_WAIT_HEADER;
static uint8_t cfgBuffer[CFG_MAX_SIZE] = {0};

/*******************************************************************************
 * Global Subroutine Structure
 ******************************************************************************/
st_subroutine gstSR_BLE_UART = {
		.pfnInit = Init,
		.pfnRun = Run,
		.subroutine_name = "gstSR_BLE_UART"
};

/*******************************************************************************
 * Function Definitions
 ******************************************************************************/

/*******************************************************************************
 * UART Reception Control
 ******************************************************************************/

/**
 * @brief Initializes UART7 reception for BLE command handling.
 *
 * @details This function sets the initial UART receive state to wait for the
 *          header, and initiates DMA-based reception for the header bytes.
 */
void UART7_StartReception(void)
{
	uart_rx_state = UART_RX_WAIT_HEADER;  // Set UART state to expect header

	uart_rx_payload = USBReadBuffer;  // Points to full packet buffer:
	                                  // [0–5]=Header, [6–N]=Payload, [N+1–N+4]=CRC

	// uart_rx_payload is assigned from statically allocated USBReadBuffer
	// Klocwork false-positive: buffer is always valid

	// Clear entire buffer sections before new reception
	clear_UART_rx_buffers();

	// Start DMA reception for the UART command header
	if (HAL_UART_Receive_DMA(&UART7_Handle, uart_rx_header,
			UART_CMD_HEADER_SIZE) != HAL_OK)
	{
		// Set error state if DMA reception fails
		uart_rx_state = UART_RX_ERROR;
		// restart reception
        UART_SendResponse(BLE_NACK);
        UART7_StartReception();
        return;
	}
	return;
}

/**
 * @brief Continues reception of UART payload via DMA.
 *
 * @details Calculates remaining bytes and initiates DMA-based payload
 *          reception in configurable chunk sizes.
 */
void UART7_ContinuePayloadReception(void)
{
	uint32_t remaining = 0U;
	uint32_t chunk_size = 0U;

	// Calculate remaining payload bytes
    remaining = uart_expected_payload_length -
    		(uart_rx_offset - UART_CMD_HEADER_SIZE);

    // Limit chunk size to UART DMA max chunk size
    chunk_size = (remaining > UART_DMA_MAX_CHUNK) ?
    		UART_DMA_MAX_CHUNK : remaining;

    // Start DMA to the correct offset
    if (HAL_UART_Receive_DMA(&UART7_Handle,
            &uart_rx_payload[uart_rx_offset], chunk_size) != HAL_OK)
    {
        uart_rx_state = UART_RX_ERROR;
        // restart reception
        UART_SendResponse(BLE_NACK);
        UART7_StartReception();
        return;
    }

    // Increment offset only after successful DMA start
    uart_rx_offset += chunk_size;
}

/**
 * @brief UART receive complete callback.
 *
 * @details Handles BLE UART receive state transitions: header,
 *          payload, and CRC. Uses DMA for chunked reception.
 *
 * @param huart UART handle pointer
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    uint32_t remaining = 0U;
    uint32_t chunk_size = 0U;

    // Only proceed if the callback is for UART7
    if ((huart == NULL) || (huart->Instance != UART7))
    {
        return;
    }

    switch (uart_rx_state)
    {
    case UART_RX_WAIT_HEADER:
        // Validate start byte
        if (uart_rx_header[0] != UART_START_BYTE)
        {
            UART_SendResponse(BLE_NACK);   // Invalid start byte
            UART7_StartReception();        // Restart reception
            return;
        }

        // Extract payload length (little-endian from header[2]..[5])
        uart_expected_payload_length = ((uint32_t)uart_rx_header[2]) |
                                       ((uint32_t)uart_rx_header[3] << 8) |
                                       ((uint32_t)uart_rx_header[4] << 16) |
                                       ((uint32_t)uart_rx_header[5] << 24);

        // Validate payload length
        if (uart_expected_payload_length > UART_CMD_MAX_PAYLOAD_SIZE)
        {
            UART_SendResponse(BLE_NACK);   // Payload too large
            UART7_StartReception();
            return;
        }

        // reset offset
        uart_rx_offset = UART_CMD_HEADER_SIZE;  // Reserve first 6 bytes for header

        if (uart_expected_payload_length == 0U)
        {
            uart_rx_state = UART_RX_WAIT_CRC;

            if (HAL_UART_Receive_DMA(&UART7_Handle, uart_rx_crc, UART_CMD_CRC_SIZE) != HAL_OK)
            {
                uart_rx_state = UART_RX_ERROR;
                // restart reception
                UART_SendResponse(BLE_NACK);
                UART7_StartReception();
                return;
            }
        }
        else
        {
            uart_rx_state = UART_RX_WAIT_PAYLOAD;
            UART7_ContinuePayloadReception();
        }
        break;

    case UART_RX_WAIT_PAYLOAD:
        // Calculate and update chunk reception progress
        remaining = uart_expected_payload_length - uart_rx_offset;
        chunk_size = (remaining > UART_DMA_MAX_CHUNK) ? UART_DMA_MAX_CHUNK : remaining;
        uart_rx_offset += chunk_size;

        if (uart_rx_offset < uart_expected_payload_length)
        {
            UART7_ContinuePayloadReception();
        }
        else
        {
            // All payload received, now receive CRC
            uart_rx_state = UART_RX_WAIT_CRC;

            if (HAL_UART_Receive_DMA(&UART7_Handle, uart_rx_crc, UART_CMD_CRC_SIZE) != HAL_OK)
            {
                uart_rx_state = UART_RX_ERROR;
                // restart reception
                UART_SendResponse(BLE_NACK);
                UART7_StartReception();
                return;
            }
        }
        break;

    case UART_RX_WAIT_CRC:
        uart_rx_state = UART_RX_COMPLETE;

        // Build the full packet: copy header and CRC into uart_rx_payload
        memcpy(&uart_rx_payload[0], uart_rx_header, UART_CMD_HEADER_SIZE);  // Copy header at start
        memcpy(&uart_rx_payload[UART_CMD_HEADER_SIZE + uart_expected_payload_length],
               uart_rx_crc, UART_CMD_CRC_SIZE);  // Append CRC at end

        // Process the complete packet (header + payload + CRC)
        Process_UART_Command(uart_rx_payload, uart_expected_payload_length);

        // Prepare for next reception
        UART7_StartReception();
        break;

    default:
        // Invalid state
        UART_SendResponse(BLE_NACK);
        UART7_StartReception();
        break;
    }
}


/**
 * @brief UART error callback.
 *
 * @details Resets reception by restarting the UART reception state machine.
 *
 * @param huart UART handle
 */
void HAL_UART_ErrorCallback(UART_HandleTypeDef *huart)
{
	if ((huart != NULL) && (huart->Instance == UART7))
	{
		UART7_StartReception();    // Restart reception on error
	}
}

/*******************************************************************************
 * Command Handling and Packet Processing
 ******************************************************************************/

/**
 * @brief Processes a complete UART command received over BLE UART.
 *
 * @details This function performs the following steps:
 *          1. Calculates total packet length (header + payload).
 *          2. Extracts the 4-byte CRC32 from the end of the packet.
 *          3. Extracts the command type from the header.
 *          4. Validates the CRC32 to ensure data integrity.
 *          5. Dispatches the command based on the command type.
 *
 * @param[in] fullPacket     Pointer to full command buffer, starting from
 *                           index 0 (header), followed by payload and CRC.
 * @param[in] payloadLength  Length of the payload in bytes (excluding header
 *                           and CRC).
 *
 * @return None
 */
void Process_UART_Command(uint8_t *fullPacket, uint32_t payloadLength)
{
    uint32_t total_len = 0U;     /* Total size of header + payload */
    uint32_t received_crc = 0U;  /* CRC32 value extracted from packet */
    uint8_t packet_type = 0U;    /* Command type extracted from header */

    /* Calculate total length of the received data (excluding CRC) */
    total_len = UART_CMD_HEADER_SIZE + payloadLength;

    /* Extract the 4-byte CRC32 value from the end of the packet */
    received_crc = ExtractCRC32(&fullPacket[total_len]);

    /* Extract the command type from the predefined header byte index */
    packet_type = fullPacket[UART_CMD_TYPE_INDEX];

    /* Validate CRC32 over header and payload */
    if (!validate_crc(fullPacket, total_len, received_crc))
    {
    	UART_SendResponse(BLE_NACK);  /* Send NACK if CRC fails */
        return;
    }

    /* Dispatch based on command type */
    switch (packet_type)
    {
        case BLE_CONFIG_REQUEST:
            /* Send configuration data back to requester */
            SendBLEConfigResponse();
            break;

        case BLE_CONFIG_WRITE:
            /* Write received configuration data to FRAM */
        	writeConfigurationToNexus(fullPacket + UART_CMD_HEADER_SIZE,
        			payloadLength);
            break;

        default:
            /* Unknown or unsupported command type */
            UART_SendResponse(BLE_NACK);
            break;
    }
}


/*******************************************************************************
 * Response Utilities
 ******************************************************************************/

/**
 * @brief Sends a basic ACK/NACK UART response.
 *
 * @details Constructs a response frame with 0-length payload and appends CRC.
 *
 * @param type Response type (ACK or NACK)
 */
void UART_SendResponse(uint8_t type)
{
	uint8_t response[UART_CMD_HEADER_SIZE + UART_CMD_CRC_SIZE] = {0};
	uint32_t crc = 0U;

	// Construct minimal response packet (header + CRC, no payload)
	response[0] = UART_START_BYTE;
	response[1] = type;
	response[2] = 0; response[3] = 0;
	response[4] = 0; response[5] = 0;

	// Compute CRC on header
	crc = crc32(response, UART_CMD_HEADER_SIZE);
	// Append CRC
	memcpy(&response[6], &crc, UART_CMD_CRC_SIZE);

	// Transmit the response
	if (HAL_UART_Transmit(&UART7_Handle, response, sizeof(response),
			HAL_MAX_DELAY) != HAL_OK)
	{
		uart_rx_state = UART_TX_ERROR; // Update state if failed
	}
}

/**
 * @brief Sends a UART response with header, payload, and appended CRC32.
 *
 * @param type    Response packet type
 * @param payload Pointer to the payload buffer
 * @param length  Payload length in bytes
 */
void UART_SendPayloadResponse(uint8_t type, const uint8_t *payload,
		uint32_t length)
{
	uint32_t total_len = 0U;
	uint32_t crc = 0U;

	total_len = UART_CMD_HEADER_SIZE + length;

	// clamp to max allowed payload size
	if (length > MAX_PKT_SIZE)
	{
		length = MAX_PKT_SIZE;
	}

	// Construct response header
	uart_rx_payload[0] = UART_START_BYTE;
	uart_rx_payload[1] = type;
	uart_rx_payload[2] = (uint8_t)(length & 0xFFU);
	uart_rx_payload[3] = (uint8_t)((length >> 8) & 0xFFU);
	uart_rx_payload[4] = (uint8_t)((length >> 16) & 0xFFU);
	uart_rx_payload[5] = (uint8_t)((length >> 24) & 0xFFU);

	// Copy payload into buffer
	memcpy(&uart_rx_payload[UART_CMD_HEADER_SIZE], payload, length);

	// Compute CRC on header + payload
	crc = crc32(uart_rx_payload, total_len);

	// Append CRC (little endian)
	uart_rx_payload[total_len + 0U] = (uint8_t)(crc & 0xFFU);
	uart_rx_payload[total_len + 1U] = (uint8_t)((crc >> 8) & 0xFFU);
	uart_rx_payload[total_len + 2U] = (uint8_t)((crc >> 16) & 0xFFU);
	uart_rx_payload[total_len + 3U] = (uint8_t)((crc >> 24) & 0xFFU);

	// Transmit full packet (header + payload + CRC)
	if (HAL_UART_Transmit(&UART7_Handle, uart_rx_payload, total_len +
			UART_CMD_CRC_SIZE, HAL_MAX_DELAY) != HAL_OK)
	{
		uart_rx_state = UART_TX_ERROR; // Update state if failed
	}
}


/*******************************************************************************
 * BLE Configuration Interface
 ******************************************************************************/

/**
 * @brief Reads BLE configuration data from FRAM.
 *
 * @details This function calculates the total configuration size and reads the
 *          data from a predefined FRAM memory region into the caller-provided
 *          buffer. It first validates the buffer pointers and ensures the
 *          size doesn't exceed the allowed configuration buffer size to prevent
 *			memory overflows.
 *
 * @param[out] buffer Pointer to the buffer where the configuration data will
 * 			   be stored.
 * @param[out] size   Pointer to a variable where the size of the read data
 * 			   will be stored.
 *
 * @return PASS if the read is successful; otherwise, FAIL.
 */
en_pass_fail ReadBLEConfiguration(uint8_t *buffer, uint32_t *size)
{
	en_pass_fail status = FAIL;
	uint32_t address = 0U;

	/* Check if input pointers are valid */
	if ((buffer == NULL) || (size == NULL))
	{
		return FAIL;
	}

	/* Assign the starting address of the configuration block in FRAM */
	address = FRAM_START_ADDRESS__PARAMETERS_MAIN;

	/* Compute the total size of configuration data
	 * (main size + optional padding) */
	*size = FRAM_PARAMETERS_MAIN_SIZE__BYTES + PADDING_FILE_SIZE__BYTES;

	/* Verify size does not exceed maximum allowed buffer size */
	if (*size > CFG_MAX_SIZE)
	{
		return FAIL;
	}

	/* Read the configuration data from FRAM into the buffer */
	status = FRAM_ReadMemoryCode(address, buffer, *size);

	return status;
}

/**
 * @brief Sends BLE configuration response over UART7.
 *
 * @details This function invokes the configuration reader to fetch stored BLE
 *          settings from FRAM. Upon success, the data is transmitted to the
 *          BLE module via UART7 in a response packet. If reading fails, an
 *          error (NACK) response is transmitted instead.
 *
 * @note Uses a static buffer to hold the configuration to ensure data
 *       persistence across function calls, avoiding stack overflows on
 *       large transfers.
 *
 * @param None
 * @return None
 */
void SendBLEConfigResponse(void)
{
	uint32_t cfgSize = 0;   /* Size of config data */
	en_pass_fail status;

	/* Clear buffer to hold config data */
	memset(cfgBuffer, 0, CFG_MAX_SIZE);

	/* Attempt to read configuration from FRAM */
	status = ReadBLEConfiguration(cfgBuffer, &cfgSize);

	if (status == PASS)
	{
		/* Transmit configuration data with response type over UART */
		UART_SendPayloadResponse(BLE_CONFIG_RESPONSE, cfgBuffer, cfgSize);
	}
	else
	{
		/* Send error response if configuration read failed */
		UART_SendResponse(BLE_PKT_ERROR);
	}
}

/**
 * @brief Handles BLE configuration write using received payload.
 *
 * @details Writes the validated configuration payload to FRAM after enabling
 *          the write latch. Ensures payload size does not exceed FRAM limits.
 *          Sends ACK or NACK based on write operation result.
 *
 * @param[in] payload        Pointer to the received configuration data.
 * @param[in] payloadLength  Length of the payload in bytes.
 *
 * @return PASS if write succeeds, otherwise FAIL.
 */
en_pass_fail writeConfigurationToNexus(uint8_t *payload,
                                       uint32_t payloadLength)
{
    en_pass_fail writeStatus = FAIL;
    en_pass_fail latchStatus = FAIL;
    uint32_t framAddress = 0U;
    uint32_t maxAllowedSize = 0U;

    /* Set initial configuration */
    framAddress = FRAM_START_ADDRESS__PARAMETERS_MAIN;
    maxAllowedSize = FRAM_PARAMETERS_MAIN_SIZE__BYTES *
                     FRAM_NUM_REDUNDANT_DATA_COPIES;

    /* Validate input parameters */
    if ((payload == NULL) || (payloadLength == 0U))
    {
        UART_SendResponse(BLE_PKT_ERROR);  /* Null or empty payload */
        return FAIL;
    }

    /* Ensure payload does not exceed allowed size */
    if (payloadLength > maxAllowedSize)
    {
        UART_SendResponse(BLE_PKT_ERROR);  /* Payload too large */
        return FAIL;
    }

    /* Enable FRAM write latch */
    latchStatus = FRAM_SetWriteEnableLatch();
    if (latchStatus != PASS)
    {
        UART_SendResponse(BLE_PKT_ERROR);  /* Failed to enable latch */
        return FAIL;
    }

    /* Step 5: Write payload to FRAM */
    writeStatus = FRAM_WriteMemoryCode(framAddress, payload, payloadLength);

    if (writeStatus == PASS)
    {
        UART_SendResponse(BLE_ACK);  /* Write successful */
    }
    else
    {
        UART_SendResponse(BLE_NACK);  /* Write failed */
        writeStatus = FAIL;
    }

    /* Reset FRAM write latch after operation */
    FRAM_ResetWriteEnableLatch();

    return writeStatus;
}

/*******************************************************************************
 * CRC Utilities
 ******************************************************************************/
/**
 * @brief Reverses the bit order of the lower 'bit_count' bits of the input
 *        value.
 * @param input_data The input data whose bits are to be reversed.
 * @param bit_count Number of bits to reverse (e.g., 8 or 32).
 * @return Bit-reversed value.
 */
uint32_t reverseBitOrder(uint32_t input_data, int bit_count)
{
	uint32_t bit_reversed_value = 0U;
	int index = 0;

	/* Loop over the number of bits and reverse their order */
	for (index = 0; index < bit_count; index++)
	{
		/* If the index-th bit is set in 'input_data',
		 *  set the (bit_count - 1 - index)th bit in result */
		if ((input_data & (1UL << index)) != 0U)
		{
			bit_reversed_value |= (1UL << ((bit_count - 1) - index));
		}
	}
	return bit_reversed_value;
}

/**
 * @brief Computes the CRC32 checksum for a given byte buffer.
 *
 * @details This function implements a software-based CRC32 calculation using
 *          the standard polynomial 0x04C11DB7. The algorithm includes bit
 *          reflection of each byte and the final CRC result, and operates on
 *          each bit to maintain correctness for embedded communication.
 *
 * @param data  Pointer to the input data buffer over which the CRC is computed.
 * @param length Number of bytes in the data buffer.
 * @return Computed 32-bit CRC value.
 */
uint32_t crc32(const uint8_t *data, uint32_t length)
{
	uint32_t crc = CRC32_INIT_VALUE;   /* Init CRC with all bits set */
	uint32_t poly = CRC32_POLYNOMIAL;  /* Standard CRC32 polynomial */
	uint32_t i = 0U;                   /* Byte loop counter */
	uint32_t j = 0U;                   /* Bit loop counter */
	uint8_t byte = 0U;                 /* Current reflected byte */

	for (i = 0U; i < length; i++)
	{
		/* Reflect the current byte before processing */
		byte = (uint8_t)reverseBitOrder(data[i], 8);

		/* XOR the byte into the highest byte of the CRC */
		crc ^= ((uint32_t)byte << 24);

		/* Process each bit in the current byte */
		for (j = 0U; j < 8U; j++)
		{
			if ((crc & 0x80000000UL) != 0U)
			{
				/* If MSB is set, shift left and XOR with poly */
				crc = (crc << 1) ^ poly;
			}
			else
			{
				/* Else, shift left only */
				crc <<= 1;
			}
		}

		/* Ensure CRC stays within 32 bits */
		crc &= CRC32_INIT_VALUE;
	}

	/* Reflect final CRC and XOR with 0xFFFFFFFF to finalize */
	crc = reverseBitOrder(crc, 32U);
	crc = crc ^ CRC32_INIT_VALUE;

	return crc;
}

/**
 * @brief Verifies the integrity of received data using CRC32.
 *
 * @details This function calculates the CRC32 checksum over the provided data
 *          and compares it with the expected (received) CRC. It is typically
 *          used to validate UART or other communication packet integrity.
 *
 * @param data           Pointer to the input data buffer (header + payload).
 * @param length         Length of the input buffer in bytes.
 * @param crc_in_packet  Expected CRC value received with the packet.
 * @return true if the computed CRC matches the expected CRC; false otherwise.
 */
bool validate_crc(const uint8_t *data, uint32_t length, uint32_t crc_in_packet)
{
	uint32_t crc_calc = 0U;     /* CRC computed over the input data */
	bool is_valid = false;      /* Flag indicating CRC validation result */

	/* Calculate CRC on input data */
	crc_calc = crc32(data, length);

	/* Compare with expected CRC value */
	is_valid = (crc_calc == crc_in_packet) ? true : false;

	return is_valid;
}

/**
 * @brief Extracts a 32-bit CRC value from a 4-byte buffer.
 *
 * @param crc_bytes Pointer to the 4-byte CRC buffer
 * @return Extracted 32-bit CRC value
 */
uint32_t ExtractCRC32(const uint8_t *crc_bytes)
{
	if (crc_bytes == NULL)
	{
		return 0U;
	}

	return ((uint32_t)crc_bytes[0] << 24) |
			((uint32_t)crc_bytes[1] << 16) |
			((uint32_t)crc_bytes[2] << 8)  |
			((uint32_t)crc_bytes[3]);
}

/*******************************************************************************
 * Subroutine Lifecycle
 ******************************************************************************/

/**
 * @brief Initializes BLE UART subroutine structure.
 *
 * @details Sets run intervals and triggers UART reception.
 */
static void Init(void)
{
	gstSR_BLE_UART.uwFirstRunDelay_1ms = SUBROUTINE_FIRST_RUN_DELAY_1MS;
	gstSR_BLE_UART.uwRunInterval_1ms = SUBROUTINE_RUN_INTERVAL_1MS;
	UART7_StartReception();
}

/**
 * @brief Periodic subroutine tick handler.
 *
 * @details Retries UART reception if previous state encountered an error.
 */
static void Run(void)
{
	/* Intentionally kept Empty: Reserved for future */
}


/**
 * @brief Clears UART reception buffers: header, payload, and CRC.
 *
 * @details Ensures clean state before beginning UART reception.
 */
void clear_UART_rx_buffers(void)
{
    memset(uart_rx_header, 0, UART_CMD_HEADER_SIZE);
    memset(uart_rx_payload, 0, UART_CMD_MAX_PAYLOAD_SIZE);
    memset(uart_rx_crc, 0, UART_CMD_CRC_SIZE);
}





