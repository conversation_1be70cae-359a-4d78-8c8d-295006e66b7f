/*******************************************************************************
 * @Copyright (C) 2022 by Vantage Elevation
 * @file           : app.h
 * @version 		  : 1.0.0
 * @brief          : Main program body
 * @details		  : Main program body declarations
 ********************************************************************************/
#ifndef _APP_H_
#define _APP_H_
/*******************************************************************************
 * Includes
 ********************************************************************************/
#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include "main.h"
#include "fram_def.h"
#include "temp_eflash_loader.h"
#include "stm32h7xx_hal.h"
#include "scheduler.h"
#include "system.h"
#include "version.h"
#include "app_gpio.h"
#include "app_timers.h"
#include "network.h"
#include "app_crc.h"
#include "app_swv.h"
#include "stdio.h"
#include "app_can.h"
#include "app_watchdog.h"
#include "app_rtc.h"
#include "app_uart.h"

#include "sr_local_inputs.h"
#include "sr_local_outputs.h"
#include "sr_watchdog.h"
#include "sr_CAN1.h"
//#include "sr_FRAM.h"
#include "sr_car_net.h"
#include "sr_SPI_AB.h"
#include "sr_SPI_AU.h"
#include "sr_parameters.h"
#include "sr_bootloader.h"
#include "sr_ble_uart.h"

/*******************************************************************************
 * Preprocessor Constants
 ********************************************************************************/

/*******************************************************************************
 * Configuration Constants
 ********************************************************************************/

/*******************************************************************************
 * Macros
 ********************************************************************************/

/*******************************************************************************
 * Typedefs
 ********************************************************************************/

/*******************************************************************************
 * Global Variables
 ********************************************************************************/

/*******************************************************************************
 * Function Prototypes
 ********************************************************************************/

#endif /* _APP_H_ */
