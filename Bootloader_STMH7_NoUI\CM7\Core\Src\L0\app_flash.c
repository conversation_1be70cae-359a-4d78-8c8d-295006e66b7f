/*******************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file           : app_flash.c
 * @version      : 1.0.0
 * @brief Functions for interfacing with internal flash on STM32H7xx chip
 * @details Functions for interfacing with internal flash on STM32H7xx chip
 *****************************************************************************/

/******************************************************************************
 * Includes
 *******************************************************************************/
#include "app_flash.h"

/**
 * @brief  Clears pending flash error flags prior to a request
 * @param  None
 * @retval None
 */
void flash_clear_flags(void)
{
   /* Clear pending flags (if any) */
   __HAL_FLASH_CLEAR_FLAG_BANK1(FLASH_FLAG_ALL_ERRORS_BANK1);
   __HAL_FLASH_CLEAR_FLAG_BANK2(FLASH_FLAG_ALL_ERRORS_BANK2);
}

/**
 * @brief  Initializes flash read/write latencies appropriate for configured clock / voltage ranges
 * @param  None
 * @retval None
 */
void Flash_Init(void)
{
	flash_clear_flags();
}
/**
 * @brief  Deinitializes flash read/write latencies appropriate for configured clock / voltage ranges
 * @param  None
 * @retval None
 */
void Flash_DeInit(void)
{

}
