/*******************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file           : sr_CAN1.c
 * @version       : 1.0.0
 * @brief Subroutine handling CAN1 network
 * @details Subroutine handling CAN1 network
 *****************************************************************************/

/******************************************************************************
 * Includes
 *******************************************************************************/
#include "app_spi.h"
#include "sr_CAN1.h"

#include <string.h>

#include "app_crc.h"
#include "app_spi.h"
#include "network.h"
#include "sr_SPI_AB.h"
#include "sr_SPI_AU.h"
// todo rm
#include "boot_dg_car_ct_a7.h"
#include "boot_dg_car_ct_b4.h"
#include "boot_dg_car_mr_a7.h"
/******************************************************************************
 * Function Prototypes
 *******************************************************************************/
static void Init(void);
static void Run(void);

/******************************************************************************
 * Module Preprocessor Constants
 *******************************************************************************/
#define SUBROUTINE_FIRST_RUN_DELAY_1MS             (0U)
#define SUBROUTINE_RUN_INTERVAL_1MS                (1U)

// How long the bus can go without receiving a message before an offline flag is set
#define CAN_OFFLINE_TIMEOUT_1MS                    (1000U)

/******************************************************************************
 * Module Preprocessor Macros
 *******************************************************************************/

/******************************************************************************
 * Module Typedefs
 *******************************************************************************/

/******************************************************************************
 * Global Variable Definitions
 *******************************************************************************/
st_subroutine gstSR_CAN1 = { .pfnInit = Init, .pfnRun = Run, .subroutine_name = "gstSR_CAN1"};
/******************************************************************************
 * Module Variable Definitions
 *******************************************************************************/
static uint16_t uwOfflineTimer_1ms = CAN_OFFLINE_TIMEOUT_1MS;
static const en_sys_network eLocalNetwork = SYS_NETWORK__CAR;
/******************************************************************************
 * Function Definitions
 *******************************************************************************/
/* @fn en_pass_fail CAN1_TransmitDatagram(st_CAN_msg *pstMsg)
 * @brief Returns 0 if the packet was successfully loaded for transmit
 * @param pstMsg, pointer to CAN message to transmit
 * @return Returns 0 if the packet was successfully loaded for transmit
 */
en_pass_fail CAN1_TransmitDatagram(st_CAN_msg *pstMsg) {
   en_pass_fail eError = FAIL;
   if(pstMsg->bExtendedID == INACTIVE) {
      if(pstMsg->uiID & NETWORK_PACKET_ID_EXTENDED_BITMAP)
         pstMsg->bExtendedID = ACTIVE;
      else
         pstMsg->bExtendedID = INACTIVE;
   }
   pstMsg->uiID &= NETWORK_PACKET_ID_EXTENDED_MASK;
   if(CAN1_LoadToRB(pstMsg) == PASS) {
      eError = PASS;
   }
   return eError;
}

/* @fn static void UnloadDatagrams(void)
 * @brief Unloads messages from the CAN ring buffer
 * @param None
 * @return None
 */
static void UnloadDatagrams(void) {
   st_datagram_control *pstControl;
   st_datagram *pstDatagram;
   st_CAN_msg stRxMsg;
   en_sys_network eNetwork;
   en_sys_node eSource;
   uint16_t uwDatagramID;
   uint8_t ucNumBytes;
   for(uint8_t i = 0; i < CAN_RX_RING_BUFFER_SIZE; i++) {
      if(CAN1_UnloadFromRB(&stRxMsg) == PASS) {
         eNetwork = Network_GetNetworkID(stRxMsg.uiID);
         if(eNetwork == eLocalNetwork) {
            eSource = Network_GetSourceID(stRxMsg.uiID);
            pstControl = Network_GetControlStructure_Car(eSource);
            if(pstControl != 0) {
               uwDatagramID = Network_GetDatagramID(stRxMsg.uiID);
               if(uwDatagramID < pstControl->uwNumberOfDG) {
                  pstDatagram = &pstControl->pastDatagrams[uwDatagramID];
                  //ucNumBytes = pstDatagram->ucSize_Bytes;
                  ucNumBytes = Network_GetNumberOfBytes(stRxMsg.uiID);

                  /* 1. Copy the content of every datagram */
                  memcpy(pstDatagram->paucData, &stRxMsg.unData.auc8[0], ucNumBytes);
                  /* 2. Run the datagram's unload function */
                  if(pstDatagram->uiDestinationBitmap & (1 << System_GetNodeID()))
                     pstDatagram->pfnUnload();

                  /* 3. Check if the packet should be forwarded */
                  if((Network_CheckDestination_Car(NET_FWD_PATH__AB,
                                                   pstDatagram->uiDestinationBitmap)
                      == PASS)) {
                     stRxMsg.ucDLC = ucNumBytes;
                     SPI_AB_TransmitDatagram(&stRxMsg);
                  }

                  if((Network_CheckDestination_Car(NET_FWD_PATH__UI,
                                                   pstDatagram->uiDestinationBitmap)
                      == PASS)) {
//                     stRxMsg.ucDLC = ucNumBytes;
//                     st_SPI_msg stSPIRxMsg;
//                     stSPIRxMsg.uiID  = stRxMsg.uiID;
//                     stSPIRxMsg.ucDLC = stRxMsg.ucDLC;
//                     stSPIRxMsg.data  = (uint16_t*)&pstDatagram->paucData[0];
//
//                     SPI_AU_TransmitDatagram(&stSPIRxMsg);
                    st_SPI_msg stSPIRxMsg;
                    stSPIRxMsg.size        = pstDatagram->ucSize_Bytes;
                    stSPIRxMsg.datagram_id = (uint8_t)uwDatagramID;
                    stSPIRxMsg.source_id   = eSource;
                    stSPIRxMsg.network_id  = eNetwork;
                    stSPIRxMsg.data        = (uint16_t*)&pstDatagram->paucData[0];

                    SPI_AU_TransmitDatagram(&stSPIRxMsg);

                    if((uwDatagramID == BOOT_DG_CAR_MR_A7__Bootloader_Magic))
                     {
//                       stRxMsg.ucDLC = ucNumBytes;
                       memcpy(&stRxMsg.unData.auc8[0],
                              pstDatagram->paucData,
                              pstDatagram->ucSize_Bytes);
                       SPI_AU_TransmitDatagram_v1(&stRxMsg);
                     }

                  }

                  /* 4. Increment receive counter */
                  pstDatagram->ucPacketCounter++;

                  /* 5. Mark packet as received */
                  pstDatagram->bDataChanged = ACTIVE;
               }
            }
         }
         CAN1_IncrementStatusCounter(CAN_COUNTER__RX_PACKET);
         uwOfflineTimer_1ms = 0;
      } else {
         break;
      }
   }
}
/* @fn uint8_t CAN2_CheckIfCommLoss(void)
 * @brief Returns 1 if no communication has been received in the timeout period
 * @param None
 * @return Returns 1 if no communication has been received in the timeout period
 */
uint8_t CAN1_CheckIfCommLoss(void) {
   return (uwOfflineTimer_1ms >= CAN_OFFLINE_TIMEOUT_1MS);
}

/* @fn static void UpdateOfflineTimer(void)
 * @brief Checks if no messages have been received in defined the timeout period.
 * @param None
 * @return None
 */
static void UpdateOfflineTimer(void) {
   if(uwOfflineTimer_1ms >= CAN_OFFLINE_TIMEOUT_1MS) {

   } else {
      uwOfflineTimer_1ms += SUBROUTINE_RUN_INTERVAL_1MS;
   }
}
/* @fn static void Init(void)
 * @brief Initialization function for the subroutine's run interval, first run delay, and state machine variables
 * @param None
 * @return None
 */
static void Init(void) {
   gstSR_CAN1.uwFirstRunDelay_1ms = SUBROUTINE_FIRST_RUN_DELAY_1MS;
   gstSR_CAN1.uwRunInterval_1ms = SUBROUTINE_RUN_INTERVAL_1MS;
}

/* @fn static void Run(void)
 * @brief Run function for the subroutine that is executed every uwRunInterval_1ms
 * @param None
 * @return None
 */
static void Run(void) {
   UnloadDatagrams();

   UpdateOfflineTimer();

   CAN1_UpdateBusErrorCounter();

   if(CAN1_CheckForBusOffline()) {
//      Alarms_SetAlarm(CALM__BUS_RESET_MR_N1);
   }

}

