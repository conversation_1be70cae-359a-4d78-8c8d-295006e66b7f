/*******************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file           : sr_car_net_user.h
 * @version 		  : 1.0.0
 * @brief Node specific implementations of car network configuration and load/unload functions
 * @details Node specific implementations of car network configuration and load/unload functions
 *****************************************************************************/
#ifndef _SR_CAR_NET_USER_
#define _SR_CAR_NET_USER_

/******************************************************************************
 * Includes
 *******************************************************************************/
#include <stdint.h>
#include "datagram_scheduler.h"
/******************************************************************************
 * Preprocessor Constants
 *******************************************************************************/

/******************************************************************************
 * Configuration Constants
 *******************************************************************************/
#define SUBROUTINE_RUN_INTERVAL_1MS                               (1U)
/******************************************************************************
 * Macros
 *******************************************************************************/

/******************************************************************************
 * Typedefs
 *******************************************************************************/

/******************************************************************************
 * Global Variables
 *******************************************************************************/

/******************************************************************************
 * Function Prototypes
 *******************************************************************************/

st_datagram_control* CarNet_GetDatagramControlStruct();
void CarNet_Init();

#endif /* _SR_CAR_NET_USER_ */
