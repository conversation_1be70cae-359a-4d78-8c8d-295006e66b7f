/*******************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file           : sr_SPI_AB.c
 * @version       : 1.0.0
 * @brief This subroutine loads and unloads packets for transmission on SPI which connects MCUA to MCUB
 * @details This subroutine loads and unloads packets for transmission on SPI which connects MCUA to MCUB
 *****************************************************************************/

/******************************************************************************
 * Includes
 *******************************************************************************/
#include <string.h>
#include "app_crc.h"
#include "app_spi.h"
#include "network.h"
#include "sr_CAN1.h"
#include "sr_SPI_AB.h"
#include "sr_SPI_AU.h"
/******************************************************************************
 * Function Prototypes
 *******************************************************************************/
static void Init(void);
static void Run(void);

/******************************************************************************
 * Module Preprocessor Constants
 *******************************************************************************/
#define SUBROUTINE_FIRST_RUN_DELAY_1MS                            (0U)
#define SUBROUTINE_RUN_INTERVAL_1MS                               (1U)

#define UNLOAD_TIMEOUT_1MS                                        (10U)  // How long UnloadDatagrams can be called before the loop must exit

// How long the bus can go without receiving a message before an offline flag is set
#define SPI_AB_OFFLINE_TIMEOUT_1MS                               (3000U)

// How long the bus can go without receiving a message before an offline flag is set
#define MCU_B_OFFLINE_TIMEOUT_1MS                                  (3000U)
/******************************************************************************
 * Module Preprocessor Macros
 *******************************************************************************/

/******************************************************************************
 * Module Typedefs
 *******************************************************************************/

/******************************************************************************
 * Global Variable Definitions
 *******************************************************************************/
st_subroutine gstSR_SPI_AB = { .pfnInit = Init, .pfnRun = Run, .subroutine_name = "gstSR_SPI_AB"};
/******************************************************************************
 * Module Variable Definitions
 *******************************************************************************/
static uint16_t auwRxMessageBuffer[SPI_MAX_PACKET_PAYLOAD_SIZE__WORD
                                   + SPI_STARTING_INDEX_PACKET_ID
                                   + 1];// Account for STX plus 1 to guard against overrun
static uint16_t uwOfflineTimer_1ms;

static uint8_t ucRxMessageIndex;
static uint8_t bSubstituteNextValue;
static const en_sys_network eLocalNetwork = SYS_NETWORK__CAR;
static int32_t iUnloadDiff_1ms = 0;
static uint32_t uiUnloadCycleCounter = 0;
/******************************************************************************
 * Function Definitions
 *******************************************************************************/
/* @fn en_pass_fail SPI_AB_TransmitDatagram(st_CAN_msg *pstMsg)
 * @brief Takes a CAN message packet and converts it to a format for transmission on the AB network and loads it to a ring buffer for transmission
 * @param None
 * @return Returns 0 if successful in loading the requested datagram
 */
en_pass_fail SPI_AB_TransmitDatagram(st_CAN_msg *pstMsg) {
   uint32_t uiCRC;
   uint16_t auwTxMessageBuffer[SPI_MAX_PACKET_SIZE__WORD];
   uint16_t auwTempBuffer[SPI_MAX_PACKET_PAYLOAD_SIZE__WORD] = { 0 };
   uint16_t uwValue;
   uint8_t ucTempMessageIndex = 0;
   uint8_t ucTxMessageIndex = 0;

   /* Construct a temporary buffer containing just the date to be encapsulated (Packet ID + DATA + CRC) */
   *(uint32_t*) &auwTempBuffer[ucTempMessageIndex] = pstMsg->uiID;
   ucTempMessageIndex += SPI_PACKET_ID_SIZE__WORD;
   uint8_t ucWordLength = NUM_ELEMENTS_U8_TO_U16(pstMsg->ucDLC);
   for(uint8_t i = 0; i < ucWordLength; i++) {
      auwTempBuffer[ucTempMessageIndex] = pstMsg->unData.auw16[i];
      ucTempMessageIndex++;
   }
   /* For DGs that aren't aligned to 2B words, clear out the excess so it doesn't impact the CRC calculation */
   if((pstMsg->ucDLC % SPI_WORD_SIZE__BYTE) != 0)
      auwTempBuffer[ucTempMessageIndex - 1] &= 0x00FF;

   /* Data field sizes are not U32 aligned but CRC calculations are, make sure all U32 fields are a known state */
   uint32_t uiSizeInU32 = NUM_ELEMENTS_U16_TO_U32(ucTempMessageIndex);
   for(uint8_t ucWordIndex = ucTempMessageIndex;
         ucWordIndex < uiSizeInU32 * (BITMAP32_SIZE_IN_BYTES / BITMAP16_SIZE_IN_BYTES);
         ucWordIndex++) {
      auwTempBuffer[ucWordIndex] = 0;
   }
   uiCRC = CRC_Calculate((uint32_t*) &auwTempBuffer[0], uiSizeInU32);

   *(uint32_t*) &auwTempBuffer[ucTempMessageIndex] = uiCRC;
   ucTempMessageIndex += SPI_CRC_SIZE__WORD;

   /* Encode the message in the format for transmission */
   auwTxMessageBuffer[ucTxMessageIndex++] = SPI_START_OF_PACKET_SPECIAL_VALUE;
   for(uint8_t i = 0; i < ucTempMessageIndex; i++) {
      uwValue = auwTempBuffer[i];
      if((uwValue == SPI_START_OF_PACKET_SPECIAL_VALUE)
         || (uwValue == SPI_END_OF_PACKET_SPECIAL_VALUE)
         || (uwValue == SPI_SUBSTITUTE_SPECIAL_VALUE)) {
         auwTxMessageBuffer[ucTxMessageIndex++] = SPI_SUBSTITUTE_SPECIAL_VALUE;
         auwTxMessageBuffer[ucTxMessageIndex++] = ~uwValue;
      } else
         auwTxMessageBuffer[ucTxMessageIndex++] = uwValue;
   }
   auwTxMessageBuffer[ucTxMessageIndex++] = SPI_END_OF_PACKET_SPECIAL_VALUE;

   if(SPI_AB_LoadToTxRB(auwTxMessageBuffer, ucTxMessageIndex) == PASS)
      return PASS;
   else
      return FAIL;
}

/* @fn static void UnloadDatagrams(void)
 * @brief Unloads words from ring buffer, processes the words into packets and unloads them if valid
 * @param None
 * @return None
 */
static void UnloadDatagrams(void) {
   st_datagram_control *pstControl;
   st_datagram *pstDatagram;
   st_CAN_msg stRxMsg;
   en_sys_network eNetwork;
   en_sys_node eSource;
   uint32_t uiCalculatedRxCRC;
   uint32_t uiReceivedCRC;
   uint16_t uwDatagramID;
   uint8_t ucNumBytes;
   uint16_t uwValue;
   int32_t iStartTime_1ms = Timer_GetCount_1ms();
   iUnloadDiff_1ms = 0;
   uiUnloadCycleCounter = 0;
   while(uiUnloadCycleCounter < sizeof(auwRxMessageBuffer)/sizeof(uint16_t)*10)
   // while( iUnloadDiff_1ms < UNLOAD_TIMEOUT_1MS )
   {
      if( SPI_AB_UnloadFromRxRB(&uwValue) == PASS )
      {
         uiUnloadCycleCounter++;
         if(ucRxMessageIndex
            > SPI_MAX_PACKET_PAYLOAD_SIZE__WORD + SPI_STARTING_INDEX_PACKET_ID) {
            ucRxMessageIndex = 0;
            SPI_RX_IncrementCounter(SPI_AB, SPI_COUNTER__UNDERRUN);
            break;
         }
         /* Check for start of packet indicator */
         else if(uwValue == SPI_START_OF_PACKET_SPECIAL_VALUE) {
            auwRxMessageBuffer[0] = SPI_START_OF_PACKET_SPECIAL_VALUE;
            ucRxMessageIndex = SPI_STARTING_INDEX_PACKET_ID;
         } else if(uwValue == SPI_END_OF_PACKET_SPECIAL_VALUE) {
            /* If CRC and length matches, mark packet for processing */
            stRxMsg.uiID = *(uint32_t*) &auwRxMessageBuffer[SPI_STARTING_INDEX_PACKET_ID];
            stRxMsg.ucDLC = Network_GetNumberOfBytes(stRxMsg.uiID);
            uiReceivedCRC = *(uint32_t*) &auwRxMessageBuffer[ucRxMessageIndex
                                                             - SPI_CRC_SIZE__WORD];

            /* Data field sizes are not U32 aligned but CRC calculations are, make sure all U32 fields are a known state  */
            uint16_t uwDLC_InWords = NUM_ELEMENTS_U8_TO_U16(stRxMsg.ucDLC);
            uint32_t uiSizeInU32 = NUM_ELEMENTS_U16_TO_U32(uwDLC_InWords+SPI_PACKET_ID_SIZE__WORD);
            uint16_t uwStartingIndex = SPI_STARTING_INDEX_DATA + uwDLC_InWords;
            uint16_t uwEndingIndex = SPI_STARTING_INDEX_PACKET_ID
                                     + uiSizeInU32
                                       * (BITMAP32_SIZE_IN_BYTES / BITMAP16_SIZE_IN_BYTES);
            for(uint16_t uwWordIndex = uwStartingIndex; uwWordIndex < uwEndingIndex;
                  uwWordIndex++)
               auwRxMessageBuffer[uwWordIndex] = 0;

            uiCalculatedRxCRC = CRC_Calculate((uint32_t*) &auwRxMessageBuffer[SPI_STARTING_INDEX_PACKET_ID],
                                              uiSizeInU32);

            eNetwork = Network_GetNetworkID(stRxMsg.uiID);
            eSource = Network_GetSourceID(stRxMsg.uiID);
            uwDatagramID = Network_GetDatagramID(stRxMsg.uiID);
            if(uiCalculatedRxCRC == uiReceivedCRC) {
               /* If the CRC passes, convert the packet to can message format
                * Then process the packet */
               if(eNetwork == eLocalNetwork) {
                  pstControl = Network_GetControlStructure_Car(eSource);
                  if(pstControl != 0) {
                     if(uwDatagramID < pstControl->uwNumberOfDG) {
                        pstDatagram = &pstControl->pastDatagrams[uwDatagramID];
                        ucNumBytes = pstDatagram->ucSize_Bytes;
                        /* 1. Copy the content of every datagram */
                        memcpy(&pstDatagram->paucData[0],
                               &auwRxMessageBuffer[SPI_STARTING_INDEX_DATA],
                               ucNumBytes);
                        /* 2. Run the datagram's unload function */
                        if(pstDatagram->uiDestinationBitmap & (1 << System_GetNodeID()))
                           pstDatagram->pfnUnload();

                        /* 3. Check if the packet should be forwarded */
                        if((Network_CheckDestination_Car(NET_FWD_PATH__CAR,
                                                         pstDatagram->uiDestinationBitmap)
                            == PASS)) {
                           stRxMsg.bCAN_FD = 1;
                           memcpy(&stRxMsg.unData.aui32[0],
                                  &auwRxMessageBuffer[SPI_STARTING_INDEX_DATA],
                                  stRxMsg.ucDLC);
                           CAN1_TransmitDatagram(&stRxMsg);
                        }

                        if(Network_CheckDestination_Car(NET_FWD_PATH__UI,
                                                        pstDatagram->uiDestinationBitmap)
                           == PASS /*&& ( eError != FAIL ) AS todo revisit*/)
                        {
//                          st_SPI_msg stSPIRxMsg;
//                          stSPIRxMsg.uiID  = stRxMsg.uiID;
//                          stSPIRxMsg.ucDLC = stRxMsg.ucDLC;
//                          stSPIRxMsg.data  = &pstDatagram->paucData[0];
//
//                          SPI_AU_TransmitDatagram(&stSPIRxMsg);

                          st_SPI_msg stSPIRxMsg;
                          stSPIRxMsg.size        = pstDatagram->ucSize_Bytes;
                          stSPIRxMsg.datagram_id = (uint8_t)uwDatagramID;
                          stSPIRxMsg.source_id   = eSource;
                          stSPIRxMsg.network_id  = eNetwork;
                          stSPIRxMsg.data        = (uint16_t*)&pstDatagram->paucData[0];

                          SPI_AU_TransmitDatagram(&stSPIRxMsg);

//                           SPI_AU_TransmitDatagram(&stRxMsg);
                        }
                        /* 4. Increment receive counter */
                        pstDatagram->ucPacketCounter++;

                        /* 5. Mark packet as received */
                        pstDatagram->bDataChanged = ACTIVE;

                        /* 6. Reset offline timers */
                     }
                  }
               }
               SPI_RX_IncrementCounter(SPI_AB, SPI_COUNTER__RX);
               uwOfflineTimer_1ms = 0;
            } else
               SPI_RX_IncrementCounter(SPI_AB, SPI_COUNTER__CRC);
            ucRxMessageIndex = 0;
         } else if(uwValue == SPI_SUBSTITUTE_SPECIAL_VALUE)
            bSubstituteNextValue = ACTIVE;
         else {
            if(bSubstituteNextValue == ACTIVE) {
               bSubstituteNextValue = INACTIVE;
               uwValue = ~uwValue;
            }
            auwRxMessageBuffer[ucRxMessageIndex++] = uwValue;
         }
         iUnloadDiff_1ms = Timer_GetCount_1ms() - iStartTime_1ms;
      } else {
        break;
      } 
   }
}
/* @fn uint8_t SPI_AB_CheckIfCommLoss(void)
 * @brief Returns 1 if no communication has been received in the timeout period
 * @param None
 * @return Returns 1 if no communication has been received in the timeout period
 */
uint8_t SPI_AB_CheckIfCommLoss(void) {
   return (uwOfflineTimer_1ms >= SPI_AB_OFFLINE_TIMEOUT_1MS);
}
/* @fn static void UpdateOfflineTimer(void)
 * @brief Checks if no messages have been received in defined the timeout period.
 * @param None
 * @return None
 */
static void UpdateOfflineTimer(void) {
//   static uint8_t bLastOfflineFlag;
   if(uwOfflineTimer_1ms >= SPI_AB_OFFLINE_TIMEOUT_1MS) {

   } else {
      uwOfflineTimer_1ms += SUBROUTINE_RUN_INTERVAL_1MS;
   }
}
/* @fn static void Init(void)
 * @brief Initialization function for the subroutine's run interval, first run delay, and state machine variables
 * @param None
 * @return None
 */
static void Init(void) {
   gstSR_SPI_AB.uwFirstRunDelay_1ms = SUBROUTINE_FIRST_RUN_DELAY_1MS;
   gstSR_SPI_AB.uwRunInterval_1ms = SUBROUTINE_RUN_INTERVAL_1MS;
}

/* @fn static void Run(void)
 * @brief Run function for the subroutine that is executed every uwRunInterval_1ms
 * @param None
 * @return None
 */
static void Run(void) {
   UnloadDatagrams();

   UpdateOfflineTimer();
}
