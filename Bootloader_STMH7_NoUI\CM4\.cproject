<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.**********" moduleId="org.eclipse.cdt.core.settings" name="Release">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="elf" artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug" cleanCommand="rm -rf" description="" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.**********" name="Release" parent="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug">
					<folderInfo id="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.**********." name="/" resourcePath="">
						<toolChain id="com.st.stm32cube.ide.mcu.gnu.managedbuild.toolchain.exe.debug.35625196" name="MCU ARM GCC" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.toolchain.exe.debug">
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_mcu.1846838800" name="MCU" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_mcu" useByScannerDiscovery="true" value="STM32H745BITx" valueType="string"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_cpuid.2073420992" name="CPU" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_cpuid" useByScannerDiscovery="false" value="1" valueType="string"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_coreid.1340220235" name="Core" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_coreid" useByScannerDiscovery="false" value="0" valueType="string"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.fpu.309036110" name="Floating-point unit" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.fpu" useByScannerDiscovery="true" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.fpu.value.fpv4-sp-d16" valueType="enumerated"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.floatabi.58556989" name="Floating-point ABI" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.floatabi" useByScannerDiscovery="true" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.floatabi.value.hard" valueType="enumerated"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_board.1481378925" name="Board" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_board" useByScannerDiscovery="false" value="genericBoard" valueType="string"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.defaults.921364603" name="Defaults" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.defaults" useByScannerDiscovery="false" value="com.st.stm32cube.ide.common.services.build.inputs.revA.1.0.6 || Debug || true || Executable || com.st.stm32cube.ide.mcu.gnu.managedbuild.option.toolchain.value.workspace || STM32H745BITx || 1 || 0 || arm-none-eabi- || ${gnu_tools_for_stm32_compiler_path} || ../Core/Inc | ../../Drivers/STM32H7xx_HAL_Driver/Inc | ../../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy | ../../Drivers/CMSIS/Device/ST/STM32H7xx/Include | ../../Drivers/CMSIS/Include ||  ||  || CORE_CM4 | USE_HAL_DRIVER | STM32H745xx ||  || Core/Src | Drivers | Core/Startup | Common ||  ||  || ${workspace_loc:/${ProjName}/STM32H745BITX_FLASH.ld} || true || NonSecure ||  || secure_nsclib.o ||  || None ||  ||  || " valueType="string"/>
							<option id="com.st.stm32cube.ide.mcu.debug.option.cpuclock.1008821867" name="Cpu clock frequence" superClass="com.st.stm32cube.ide.mcu.debug.option.cpuclock" useByScannerDiscovery="false" value="240" valueType="string"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.convertbinary.97425540" name="Convert to binary file (-O binary)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.convertbinary" useByScannerDiscovery="false" value="true" valueType="boolean"/>
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.targetplatform.81319649" isAbstract="false" osList="all" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.targetplatform"/>
							<builder buildPath="${workspace_loc:/Bootloader_STM32H7_NoUI_CM4}/Debug" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.builder.1673135596" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Gnu Make Builder" parallelBuildOn="true" parallelizationNumber="optimal" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.builder"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.1249490103" name="MCU GCC Assembler" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler">
								<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.debuglevel.854438122" name="Debug level" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.debuglevel" useByScannerDiscovery="false" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.debuglevel.value.g3" valueType="enumerated"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.definedsymbols.887340525" name="Define symbols (-D)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.definedsymbols" useByScannerDiscovery="false" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="DEBUG"/>
								</option>
								<inputType id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.input.1886894587" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.input"/>
							</tool>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.**********" name="MCU GCC Compiler" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler">
								<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.debuglevel.1944051017" name="Debug level" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.debuglevel" useByScannerDiscovery="false" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.debuglevel.value.g3" valueType="enumerated"/>
								<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.optimization.level.345533867" name="Optimization level" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.optimization.level" useByScannerDiscovery="false" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.optimization.level.value.os" valueType="enumerated"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.definedsymbols.1088601297" name="Define symbols (-D)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.definedsymbols" useByScannerDiscovery="false" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="RELEASE_CONFIGURATION"/>
									<listOptionValue builtIn="false" value="CORE_CM4"/>
									<listOptionValue builtIn="false" value="USE_HAL_DRIVER"/>
									<listOptionValue builtIn="false" value="STM32H745xx"/>
									<listOptionValue builtIn="false" value="SYSTEM_NODE=MR_A4_NODE"/>
									<listOptionValue builtIn="false" value="BOOTLOADER_SLAVE_ONLY"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.includepaths.1664149538" name="Include paths (-I)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.includepaths" useByScannerDiscovery="false" valueType="includePath">
									<listOptionValue builtIn="false" value="../Core/Inc"/>
									<listOptionValue builtIn="false" value="../../Drivers/STM32H7xx_HAL_Driver/Inc"/>
									<listOptionValue builtIn="false" value="../../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy"/>
									<listOptionValue builtIn="false" value="../../Drivers/CMSIS/Device/ST/STM32H7xx/Include"/>
									<listOptionValue builtIn="false" value="../../Drivers/CMSIS/Include"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Bootloader_STM32H7_NoUI_CM4/Lib_System/inc/Parameters}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Bootloader_STM32H7_NoUI_CM4/Lib_System/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Bootloader_STM32H7_NoUI_CM4/Lib_System/Bootloader/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Bootloader_STM32H7_NoUI_CM4/Lib_Network/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Bootloader_STM32H7_NoUI_CM4/Lib_Network/inc/Car}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Bootloader_STM32H7_NoUI_CM4/Lib_Network/inc/Group}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Bootloader_STM32H7_NoUI_CM4/Lib_Network/inc/Hall}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Bootloader_STM32H7_NoUI_CM4/Core/Inc/L0}&quot;"/>
								</option>
								<inputType id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.input.c.885668053" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.input.c"/>
							</tool>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.958908117" name="MCU G++ Compiler" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler">
								<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.debuglevel.1828878430" name="Debug level" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.debuglevel" useByScannerDiscovery="false" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.debuglevel.value.g3" valueType="enumerated"/>
								<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.optimization.level.1304237480" name="Optimization level" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.optimization.level" useByScannerDiscovery="false"/>
							</tool>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.1279002602" name="MCU GCC Linker" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker">
								<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.option.script.967825008" name="Linker Script (-T)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.option.script" useByScannerDiscovery="false" value="${workspace_loc}\Lib_System\Bootloader\linker_scripts\STM32H745BITX_CM4_BOOTLOADER.ld" valueType="string"/>
								<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.option.verbose.976568539" name="Verbose (-Wl,--verbose)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.option.verbose" useByScannerDiscovery="false" value="true" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.option.directories.168958253" name="Library search path (-L)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.option.directories" useByScannerDiscovery="false" valueType="libPaths">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc}\Lib_System\Bootloader\linker_scripts&quot;"/>
								</option>
								<inputType id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.input.648766712" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.853620278" name="MCU G++ Linker" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.archiver.165329612" name="MCU GCC Archiver" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.archiver"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.size.347504333" name="MCU Size" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.size"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objdump.listfile.974061858" name="MCU Output Converter list file" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objdump.listfile"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.hex.196439331" name="MCU Output Converter Hex" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.hex"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.binary.1491992665" name="MCU Output Converter Binary" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.binary"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.verilog.543716760" name="MCU Output Converter Verilog" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.verilog"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.srec.671639517" name="MCU Output Converter Motorola S-rec" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.srec"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.symbolsrec.462566058" name="MCU Output Converter Motorola S-rec with symbols" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.symbolsrec"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.**********.1161885385" name="/" resourcePath="Lib_Network">
						<toolChain id="com.st.stm32cube.ide.mcu.gnu.managedbuild.toolchain.exe.debug.1353588422" name="MCU ARM GCC" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.toolchain.exe.debug" unusedChildren="">
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_mcu.1846838800.1571357976" name="MCU" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_mcu.1846838800"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_cpuid.2073420992.304635076" name="CPU" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_cpuid.2073420992"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_coreid.1340220235.1444023615" name="Core" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_coreid.1340220235"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.fpu.309036110.828127376" name="Floating-point unit" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.fpu.309036110"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.floatabi.58556989.2142157488" name="Floating-point ABI" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.floatabi.58556989"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_board.1481378925.1489622148" name="Board" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_board.1481378925"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.defaults.921364603.113845846" name="Defaults" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.defaults.921364603"/>
							<option id="com.st.stm32cube.ide.mcu.debug.option.cpuclock.1008821867.1035347648" name="Cpu clock frequence" superClass="com.st.stm32cube.ide.mcu.debug.option.cpuclock.1008821867"/>
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.targetplatform" isAbstract="false" osList="all" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.targetplatform"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.1605558627" name="MCU GCC Assembler" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.1249490103">
								<inputType id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.input.20312254" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.input"/>
							</tool>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.1339971779" name="MCU GCC Compiler" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.**********">
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.includepaths.985826131" name="Include paths (-I)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.includepaths" valueType="includePath">
									<listOptionValue builtIn="false" value="../Core/Inc"/>
									<listOptionValue builtIn="false" value="../../Drivers/STM32H7xx_HAL_Driver/Inc"/>
									<listOptionValue builtIn="false" value="../../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy"/>
									<listOptionValue builtIn="false" value="../../Drivers/CMSIS/Device/ST/STM32H7xx/Include"/>
									<listOptionValue builtIn="false" value="../../Drivers/CMSIS/Include"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Bootloader_STM32H7_NoUI_CM4/Lib_System/inc/Parameters}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Bootloader_STM32H7_NoUI_CM4/Lib_System/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Bootloader_STM32H7_NoUI_CM4/Lib_System/Bootloader/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Bootloader_STM32H7_NoUI_CM4/Lib_Network/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Bootloader_STM32H7_NoUI_CM4/Lib_Network/inc/Car}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Bootloader_STM32H7_NoUI_CM4/Lib_Network/inc/Group}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Bootloader_STM32H7_NoUI_CM4/Lib_Network/inc/Hall}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Bootloader_STM32H7_NoUI_CM4/Core/Inc/L0}&quot;"/>
								</option>
								<inputType id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.input.c.897936152" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.input.c"/>
							</tool>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.1859656947" name="MCU G++ Compiler" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.958908117"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.494214548" name="MCU GCC Linker" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.1279002602"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.779122601" name="MCU G++ Linker" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.853620278"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.archiver.1964830157" name="MCU GCC Archiver" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.archiver.165329612"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.size.800310140" name="MCU Size" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.size.347504333"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objdump.listfile.824615364" name="MCU Output Converter list file" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objdump.listfile.974061858"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.hex.1565274765" name="MCU Output Converter Hex" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.hex.196439331"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.binary.339585047" name="MCU Output Converter Binary" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.binary.1491992665"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.verilog.1597043678" name="MCU Output Converter Verilog" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.verilog.543716760"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.srec.65207831" name="MCU Output Converter Motorola S-rec" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.srec.671639517"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.symbolsrec.1885586366" name="MCU Output Converter Motorola S-rec with symbols" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.symbolsrec.462566058"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="Common"/>
						<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="Core"/>
						<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="Drivers"/>
						<entry excluding="src/network.c|src/datagram_scheduler.c|src/Hall|src/Group|src/Car|src/Control/" flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name="Lib_Network"/>
						<entry excluding="src/evt_timer.c|src/lwrb.c|src/lwrb_ex.c|src/load_weigher_def.c|src/learn_v2_def.c|src/io_def.c|src/group_def.c|src/fpga_def_old.c|src/brake_def.c|Bootloader/Startup/startup_stm32h753vitx.s|Bootloader/ports/stm32h7453xx_slave_bootloader_port|Bootloader/ports/stm32h745xx_master_slave_bootloader_port|Bootloader/ports/stm32g473xx_master_slave_bootloader_port|Bootloader/src/bootloader_master_arbitration.c|Bootloader/src/slave_only|Bootloader/src/master_slave|Bootloader/src/bootloader_config.c|src/van_user_parameters.c|src/van_stock_parameters.c|src/ulog.c|src/ui_request_def.c|src/time_def.c|src/system.c|src/security_def.c|src/scheduler.c|src/riser.c|src/ring_buffer.c|src/position_def.c|src/param_def.c|src/operation_def.c|src/no_init.c|src/motion_def.c|src/learn_def.c|src/hall_call_def.c|src/fram_def.c|src/fpga_def.c|src/floor_def.c|src/fixture_def.c|src/expansion.c|src/door_def.c|src/dispatch_def.c|src/debug_def.c|src/compliance_test_def.c|src/car_output_def.c|src/car_input_def.c|src/car_faults_def.c|src/car_call_def.c|src/car_alarms_def.c|src/app_error.c|Bootloader/src/bootloader_strings.c|Bootloader/src/network|Bootloader/src/timer.c|Bootloader/src/sr_bootloader.c|Bootloader/src/queue.c|Bootloader/src/fsm.c|Bootloader/src/bootloader.c|Bootloader/src/bootloader_update.c|Bootloader/src/bootloader_flash.c|Bootloader/src/bootloader_common.c|Bootloader/ports/stm32g473xx_slave_bootloader_port|Bootloader/ports/stm32h745xx_slave_bootloader_port|Bootloader/ports/stm32h745xx_master_bootloader_port|Bootloader/hard_fault/stm32h745xx_fault_context.s|Bootloader/src/bootloader_slave.c|Bootloader/src/bootloader_master.c|Bootloader/ports/stm32h745xx_bootloader_port|Bootloader/ports/stm32g473xx_bootloader_port|Bootloader/Startup/startup_stm32g473qetx.s" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="Lib_System"/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.pathentry"/>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="Bootloader_STM32H7_NoUI_CM4.null.348777379" name="Bootloader_STM32H7_NoUI_CM4"/>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="org.eclipse.cdt.make.core.buildtargets"/>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.**********;com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.**********.;com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.**********;com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.input.c.885668053">
			<autodiscovery enabled="false" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.661344537;com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.661344537.;com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.**********;com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.input.c.**********">
			<autodiscovery enabled="false" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
	</storageModule>
	<storageModule moduleId="refreshScope" versionNumber="2">
		<configuration configurationName="Release">
			<resource resourceType="PROJECT" workspacePath="/Bootloader_STM32H7_NoUI_CM4"/>
		</configuration>
	</storageModule>
</cproject>