<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
	<name>Bootloader_STM32H7_NoUI_CM7</name>
	<comment></comment>
	<projects>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.genmakebuilder</name>
			<triggers>clean,full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder</name>
			<triggers>full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>com.st.stm32cube.ide.mcu.MCUProjectNature</nature>
		<nature>org.eclipse.cdt.core.cnature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCUCubeIdeServicesRevAev2ProjectNature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCUCubeProjectNature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCUAdvancedStructureProjectNature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCUMultiCpuProjectNature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.managedBuildNature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature</nature>
	</natures>
	<linkedResources>
		<link>
			<name>Common</name>
			<type>2</type>
			<locationURI>PARENT-1-PROJECT_LOC/Common</locationURI>
		</link>
		<link>
			<name>Lib_Network</name>
			<type>2</type>
			<locationURI>WORKSPACE_LOC/Lib_Network</locationURI>
		</link>
		<link>
			<name>Lib_System</name>
			<type>2</type>
			<locationURI>WORKSPACE_LOC/Lib_System</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_cortex.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_crc.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_crc_ex.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_crc_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_dma.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_dma_ex.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_exti.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_fdcan.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_fdcan.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_flash.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_flash_ex.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_gpio.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_hcd.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hcd.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_hsem.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_i2c.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_i2c_ex.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_iwdg.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_iwdg.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_mdma.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_pwr.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_pwr_ex.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_rcc.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_rcc_ex.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_rtc.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rtc.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_rtc_ex.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rtc_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_spi.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_spi.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_spi_ex.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_spi_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_tim.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_tim_ex.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_uart.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_uart.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_hal_uart_ex.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_uart_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32H7xx_HAL_Driver/stm32h7xx_ll_usb.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_usb.c</locationURI>
		</link>
		<link>
			<name>Middlewares/ST/STM32_USB_Host_Library/usbh_core.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Middlewares/ST/STM32_USB_Host_Library/Core/Src/usbh_core.c</locationURI>
		</link>
		<link>
			<name>Middlewares/ST/STM32_USB_Host_Library/usbh_ctlreq.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Middlewares/ST/STM32_USB_Host_Library/Core/Src/usbh_ctlreq.c</locationURI>
		</link>
		<link>
			<name>Middlewares/ST/STM32_USB_Host_Library/usbh_ioreq.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Middlewares/ST/STM32_USB_Host_Library/Core/Src/usbh_ioreq.c</locationURI>
		</link>
		<link>
			<name>Middlewares/ST/STM32_USB_Host_Library/usbh_msc.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Src/usbh_msc.c</locationURI>
		</link>
		<link>
			<name>Middlewares/ST/STM32_USB_Host_Library/usbh_msc_bot.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Src/usbh_msc_bot.c</locationURI>
		</link>
		<link>
			<name>Middlewares/ST/STM32_USB_Host_Library/usbh_msc_scsi.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Src/usbh_msc_scsi.c</locationURI>
		</link>
		<link>
			<name>Middlewares/ST/STM32_USB_Host_Library/usbh_pipes.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Middlewares/ST/STM32_USB_Host_Library/Core/Src/usbh_pipes.c</locationURI>
		</link>
		<link>
			<name>Middlewares/Third_Party/FatFs/ccsbcs.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Middlewares/Third_Party/FatFs/src/option/ccsbcs.c</locationURI>
		</link>
		<link>
			<name>Middlewares/Third_Party/FatFs/diskio.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Middlewares/Third_Party/FatFs/src/diskio.c</locationURI>
		</link>
		<link>
			<name>Middlewares/Third_Party/FatFs/ff.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Middlewares/Third_Party/FatFs/src/ff.c</locationURI>
		</link>
		<link>
			<name>Middlewares/Third_Party/FatFs/ff_gen_drv.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Middlewares/Third_Party/FatFs/src/ff_gen_drv.c</locationURI>
		</link>
		<link>
			<name>Middlewares/Third_Party/FatFs/syscall.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Middlewares/Third_Party/FatFs/src/option/syscall.c</locationURI>
		</link>
	</linkedResources>
</projectDescription>
