/*******************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file           : sr_CAN1.h
 * @version 		  : 1.0.0
 * @brief          : CAN1 subroutine
 * @details		  : CAN1 subroutine
 ********************************************************************************/
#ifndef _SR_CAN1_
#define _SR_CAN1_

/******************************************************************************
 * Includes
 *******************************************************************************/
#include <stdint.h>
#include "app_can.h"
#include "scheduler.h"
#include "system.h"
/******************************************************************************
 * Preprocessor Constants
 *******************************************************************************/

/******************************************************************************
 * Configuration Constants
 *******************************************************************************/

/******************************************************************************
 * Macros
 *******************************************************************************/

/******************************************************************************
 * Typedefs
 *******************************************************************************/

/******************************************************************************
 * Global Variables
 *******************************************************************************/
extern st_subroutine gstSR_CAN1;
/******************************************************************************
 * Function Prototypes
 *******************************************************************************/
en_pass_fail CAN1_TransmitDatagram(st_CAN_msg *pstMsg);
uint8_t CAN1_CheckIfCommLoss(void);
#endif /* _SR_CAN1_ */
