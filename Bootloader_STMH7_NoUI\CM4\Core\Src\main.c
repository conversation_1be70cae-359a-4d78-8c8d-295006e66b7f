/* USER CODE BEGIN Header */
/**
 ******************************************************************************
 * @file           : main.c
 * @brief          : A bare bones M4 app used only when flashing the bootloader on the board.
 ******************************************************************************
 * @attention
 *
 * Copyright (c) 2023 STMicroelectronics.
 * All rights reserved.
 *
 * This software is licensed under terms that can be found in the LICENSE file
 * in the root directory of this software component.
 * If no LICENSE file comes with this software, it is provided AS-IS.
 * @note This application does serves to spin up the M4 core for its startup
 *       handshake with the M7 core. If the application M4 app is already
 *       loaded, this app should not be needed.
 *       TODO ks : WARNING, the M7 bootloader has a dependency on a valid M4 app.
 *       A bad app could potentially prevent startup of the bootloader. Investigate
 *       removing the handshake requirement on the M7 bootloader, or creating a dummy
 *       M4 bootloader just to ensure this handshake is always available.
 *
 ******************************************************************************
 */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "bootloader_cfg.h"
#include "bootloader_image.h"

/**
 * @brief  The application entry point.
 * @retval int
 */

int main(void)
{
	/* Enable HW semaphore and wait for CM7 to take the semaphore*/
	__HAL_RCC_HSEM_CLK_ENABLE();
	while(HAL_HSEM_IsSemTaken(CM4_BOOT_HSEM_ID) == 0);

	bool valid_app = Bootloader_ValidateApplication(
	    SYSTEM_NODE,
	    APPLICATION_HEADER_ADDRESS,
	    APPLICATION_ADDRESS);
	if(valid_app == true)
	{

		uint32_t *application = (uint32_t*)APPLICATION_ADDRESS;

		__set_MSP(application[0]);
		__set_CONTROL(0);

		void (*JumpToApplication)(void) = (void(*)(void)) application[1];

		JumpToApplication();
	}

	/* Not much we can do if the application fails, just hang the application here*/
	for(;;);
}

/**
 * @brief  This function is executed in case of error occurrence.
 * @retval None
 */
void Error_Handler(void) {
   /* USER CODE BEGIN Error_Handler_Debug */
   /* User can add his own implementation to report the HAL error return state */
   __disable_irq();
   while(1)
   {
   }
   /* USER CODE END Error_Handler_Debug */
}
