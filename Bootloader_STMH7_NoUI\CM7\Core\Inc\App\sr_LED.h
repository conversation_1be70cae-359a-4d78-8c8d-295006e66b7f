/*******************************************************************************
* @Copyright (C) 2022 by Vantage Elevation
* @file           : sr_LED.h
* @version 		  : 1.0.0
* @brief          : LED subroutine
* @details		  : LED subroutine
********************************************************************************/
#ifndef _SR_LED_H_
#define _SR_LED_H_
/*******************************************************************************
* Includes
********************************************************************************/
#include <stdint.h>
#include "app.h"
/*******************************************************************************
* Preprocessor Constants
********************************************************************************/


/*******************************************************************************
* Configuration Constants
********************************************************************************/


/*******************************************************************************
* Macros
********************************************************************************/


/*******************************************************************************
* Typedefs
********************************************************************************/


/*******************************************************************************
* Global Variables
********************************************************************************/
extern st_subroutine gstSR_LED;

/*******************************************************************************
* Function Prototypes
********************************************************************************/
#endif /* _SR_LED_H_ */
