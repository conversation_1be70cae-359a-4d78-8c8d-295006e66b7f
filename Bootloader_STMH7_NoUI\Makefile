##########################################################################################################################
# File automatically-generated by tool: [projectgenerator] version: [4.2.0-B44] date: [Tue Jul 23 12:30:00 CEST 2025]
##########################################################################################################################

# ------------------------------------------------
# Generic Makefile (based on gcc)
#
# ChangeLog :
#	2017-02-10 - Several enhancements + project update mode
#   2015-07-22 - first version
# ------------------------------------------------

######################################
# target
######################################
TARGET_CM7 = Bootloader_STM32H7_NoUI_CM7
TARGET_CM4 = Bootloader_STM32H7_NoUI_CM4

######################################
# building variables
######################################
# debug build?
DEBUG = 1
# optimization
OPT = -Og

#######################################
# paths
#######################################
# Build path
BUILD_DIR_CM7 = build/CM7
BUILD_DIR_CM4 = build/CM4

######################################
# source
######################################
# C sources for CM7
C_SOURCES_CM7 =  \
CM7/Core/Src/main.c \
CM7/Core/Src/stm32h7xx_it.c \
CM7/Core/Src/stm32h7xx_hal_msp.c \
CM7/Core/Src/i2c.c \
CM7/Core/Src/spi.c \
CM7/FATFS/App/fatfs.c \
CM7/FATFS/Target/usbh_diskio.c \
CM7/USB_HOST/App/usb_host.c \
CM7/USB_HOST/Target/usbh_conf.c \
CM7/USB_HOST/Target/usbh_platform.c \
Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_spi.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_spi_ex.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hcd.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_usb.c \
Middlewares/Third_Party/FatFs/src/ff.c \
Middlewares/Third_Party/FatFs/src/ff_gen_drv.c \
Middlewares/Third_Party/FatFs/src/diskio.c \
Middlewares/Third_Party/FatFs/src/option/syscall.c \
Middlewares/Third_Party/FatFs/src/option/unicode.c \
Middlewares/ST/STM32_USB_Host_Library/Core/Src/usbh_core.c \
Middlewares/ST/STM32_USB_Host_Library/Core/Src/usbh_ctlreq.c \
Middlewares/ST/STM32_USB_Host_Library/Core/Src/usbh_ioreq.c \
Middlewares/ST/STM32_USB_Host_Library/Core/Src/usbh_pipes.c \
Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Src/usbh_msc.c \
Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Src/usbh_msc_bot.c \
Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Src/usbh_msc_scsi.c

# C sources for CM4
C_SOURCES_CM4 =  \
CM4/Core/Src/main.c \
CM4/Core/Src/stm32h7xx_it.c \
CM4/Core/Src/stm32h7xx_hal_msp.c \
CM4/Core/Src/i2c.c \
CM4/Core/Src/spi.c \
Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_spi.c \
Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_spi_ex.c

# ASM sources
ASM_SOURCES_CM7 = \
../Lib_System/Bootloader/Startup/startup_stm32h745bitx.s

ASM_SOURCES_CM4 = \
../Lib_System/Bootloader/Startup/startup_stm32h745bitx.s

#######################################
# binaries
#######################################
PREFIX = arm-none-eabi-
# The gcc compiler bin path can be either defined in make command via GCC_PATH variable (> make GCC_PATH=xxx)
# either it can be added to the PATH environment variable.
ifdef GCC_PATH
CC = $(GCC_PATH)/$(PREFIX)gcc
AS = $(GCC_PATH)/$(PREFIX)gcc -x assembler-with-cpp
CP = $(GCC_PATH)/$(PREFIX)objcopy
SZ = $(GCC_PATH)/$(PREFIX)size
else
CC = $(PREFIX)gcc
AS = $(PREFIX)gcc -x assembler-with-cpp
CP = $(PREFIX)objcopy
SZ = $(PREFIX)size
endif
HEX = $(CP) -O ihex
BIN = $(CP) -O binary -S

#######################################
# CFLAGS
#######################################
# cpu for CM7
CPU_CM7 = -mcpu=cortex-m7

# fpu for CM7
FPU_CM7 = -mfpu=fpv5-d16

# float-abi for CM7
FLOAT-ABI_CM7 = -mfloat-abi=hard

# cpu for CM4
CPU_CM4 = -mcpu=cortex-m4

# fpu for CM4
FPU_CM4 = -mfpu=fpv4-sp-d16

# float-abi for CM4
FLOAT-ABI_CM4 = -mfloat-abi=hard

# mcu
MCU_CM7 = $(CPU_CM7) -mthumb $(FPU_CM7) $(FLOAT-ABI_CM7)
MCU_CM4 = $(CPU_CM4) -mthumb $(FPU_CM4) $(FLOAT-ABI_CM4)

# macros for gcc
# AS defines
AS_DEFS = 

# C defines for CM7
C_DEFS_CM7 =  \
-DUSE_HAL_DRIVER \
-DSTM32H745xx \
-DCORE_CM7

# C defines for CM4
C_DEFS_CM4 =  \
-DUSE_HAL_DRIVER \
-DSTM32H745xx \
-DCORE_CM4

# AS includes
AS_INCLUDES = 

# C includes for CM7
C_INCLUDES_CM7 =  \
-ICM7/Core/Inc \
-ICM7/FATFS/App \
-ICM7/FATFS/Target \
-ICM7/USB_HOST/App \
-ICM7/USB_HOST/Target \
-IDrivers/STM32H7xx_HAL_Driver/Inc \
-IDrivers/STM32H7xx_HAL_Driver/Inc/Legacy \
-IMiddlewares/Third_Party/FatFs/src \
-IMiddlewares/ST/STM32_USB_Host_Library/Core/Inc \
-IMiddlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc \
-IDrivers/CMSIS/Device/ST/STM32H7xx/Include \
-IDrivers/CMSIS/Include \
-I../Lib_System/Bootloader/inc \
-I../Lib_System/inc \
-I../Lib_Network/inc

# C includes for CM4
C_INCLUDES_CM4 =  \
-ICM4/Core/Inc \
-IDrivers/STM32H7xx_HAL_Driver/Inc \
-IDrivers/STM32H7xx_HAL_Driver/Inc/Legacy \
-IDrivers/CMSIS/Device/ST/STM32H7xx/Include \
-IDrivers/CMSIS/Include \
-I../Lib_System/Bootloader/inc \
-I../Lib_System/inc \
-I../Lib_Network/inc

# compile gcc flags
ASFLAGS_CM7 = $(MCU_CM7) $(AS_DEFS) $(AS_INCLUDES) $(OPT) -Wall -fdata-sections -ffunction-sections
ASFLAGS_CM4 = $(MCU_CM4) $(AS_DEFS) $(AS_INCLUDES) $(OPT) -Wall -fdata-sections -ffunction-sections

CFLAGS_CM7 = $(MCU_CM7) $(C_DEFS_CM7) $(C_INCLUDES_CM7) $(OPT) -Wall -fdata-sections -ffunction-sections
CFLAGS_CM4 = $(MCU_CM4) $(C_DEFS_CM4) $(C_INCLUDES_CM4) $(OPT) -Wall -fdata-sections -ffunction-sections

ifeq ($(DEBUG), 1)
CFLAGS_CM7 += -g -gdwarf-2
CFLAGS_CM4 += -g -gdwarf-2
endif

# Generate dependency information
CFLAGS_CM7 += -MMD -MP -MF"$(@:%.o=%.d)"
CFLAGS_CM4 += -MMD -MP -MF"$(@:%.o=%.d)"

#######################################
# LDFLAGS
#######################################
# link script for CM7
LDSCRIPT_CM7 = ../Lib_System/Bootloader/linker_scripts/STM32H745BITX_CM7_BOOTLOADER.ld

# link script for CM4
LDSCRIPT_CM4 = ../Lib_System/Bootloader/linker_scripts/STM32H745BITX_CM4_BOOTLOADER.ld

# libraries
LIBS = -lc -lm -lnosys 
LIBDIR = 
LDFLAGS_CM7 = $(MCU_CM7) -specs=nano.specs -T$(LDSCRIPT_CM7) $(LIBDIR) $(LIBS) -Wl,-Map=$(BUILD_DIR_CM7)/$(TARGET_CM7).map,--cref -Wl,--gc-sections
LDFLAGS_CM4 = $(MCU_CM4) -specs=nano.specs -T$(LDSCRIPT_CM4) $(LIBDIR) $(LIBS) -Wl,-Map=$(BUILD_DIR_CM4)/$(TARGET_CM4).map,--cref -Wl,--gc-sections

# default action: build all
all: $(BUILD_DIR_CM7)/$(TARGET_CM7).elf $(BUILD_DIR_CM7)/$(TARGET_CM7).hex $(BUILD_DIR_CM7)/$(TARGET_CM7).bin $(BUILD_DIR_CM4)/$(TARGET_CM4).elf $(BUILD_DIR_CM4)/$(TARGET_CM4).hex $(BUILD_DIR_CM4)/$(TARGET_CM4).bin

#######################################
# build the application
#######################################
# list of objects for CM7
OBJECTS_CM7 = $(addprefix $(BUILD_DIR_CM7)/,$(notdir $(C_SOURCES_CM7:.c=.o)))
vpath %.c $(sort $(dir $(C_SOURCES_CM7)))
# list of ASM program objects for CM7
OBJECTS_CM7 += $(addprefix $(BUILD_DIR_CM7)/,$(notdir $(ASM_SOURCES_CM7:.s=.o)))
vpath %.s $(sort $(dir $(ASM_SOURCES_CM7)))

# list of objects for CM4
OBJECTS_CM4 = $(addprefix $(BUILD_DIR_CM4)/,$(notdir $(C_SOURCES_CM4:.c=.o)))
vpath %.c $(sort $(dir $(C_SOURCES_CM4)))
# list of ASM program objects for CM4
OBJECTS_CM4 += $(addprefix $(BUILD_DIR_CM4)/,$(notdir $(ASM_SOURCES_CM4:.s=.o)))
vpath %.s $(sort $(dir $(ASM_SOURCES_CM4)))

# CM7 build rules
$(BUILD_DIR_CM7)/%.o: %.c Makefile | $(BUILD_DIR_CM7)
	$(CC) -c $(CFLAGS_CM7) -Wa,-a,-ad,-alms=$(BUILD_DIR_CM7)/$(notdir $(<:.c=.lst)) $< -o $@

$(BUILD_DIR_CM7)/%.o: %.s Makefile | $(BUILD_DIR_CM7)
	$(AS) -c $(CFLAGS_CM7) $< -o $@

$(BUILD_DIR_CM7)/$(TARGET_CM7).elf: $(OBJECTS_CM7) Makefile
	$(CC) $(OBJECTS_CM7) $(LDFLAGS_CM7) -o $@
	$(SZ) $@

$(BUILD_DIR_CM7)/%.hex: $(BUILD_DIR_CM7)/%.elf | $(BUILD_DIR_CM7)
	$(HEX) $< $@
	
$(BUILD_DIR_CM7)/%.bin: $(BUILD_DIR_CM7)/%.elf | $(BUILD_DIR_CM7)
	$(BIN) $< $@	

# CM4 build rules
$(BUILD_DIR_CM4)/%.o: %.c Makefile | $(BUILD_DIR_CM4)
	$(CC) -c $(CFLAGS_CM4) -Wa,-a,-ad,-alms=$(BUILD_DIR_CM4)/$(notdir $(<:.c=.lst)) $< -o $@

$(BUILD_DIR_CM4)/%.o: %.s Makefile | $(BUILD_DIR_CM4)
	$(AS) -c $(CFLAGS_CM4) $< -o $@

$(BUILD_DIR_CM4)/$(TARGET_CM4).elf: $(OBJECTS_CM4) Makefile
	$(CC) $(OBJECTS_CM4) $(LDFLAGS_CM4) -o $@
	$(SZ) $@

$(BUILD_DIR_CM4)/%.hex: $(BUILD_DIR_CM4)/%.elf | $(BUILD_DIR_CM4)
	$(HEX) $< $@
	
$(BUILD_DIR_CM4)/%.bin: $(BUILD_DIR_CM4)/%.elf | $(BUILD_DIR_CM4)
	$(BIN) $< $@	

# Create build directories
$(BUILD_DIR_CM7):
	mkdir -p $@

$(BUILD_DIR_CM4):
	mkdir -p $@

#######################################
# clean up
#######################################
clean:
	-rm -fR $(BUILD_DIR_CM7) $(BUILD_DIR_CM4)
  
#######################################
# dependencies
#######################################
-include $(wildcard $(BUILD_DIR_CM7)/*.d)
-include $(wildcard $(BUILD_DIR_CM4)/*.d)

# *** EOF ***
