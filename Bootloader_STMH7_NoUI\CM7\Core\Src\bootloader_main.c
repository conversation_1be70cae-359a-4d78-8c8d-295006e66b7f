/**
 ******************************************************************************
 * @file           : main_impl.c
 * @brief          : Main program body implementation for MR A bootloader node
 ******************************************************************************
 *       TODO ks : WARNING, the M7 bootloader has a dependency on a valid M4 app.
 *       A bad app could potentially prevent startup of the bootloader. Investigate
 *       removing the handshake requirement on the M7 bootloader, or creating a dummy
 *       M4 bootloader just to ensure this handshake is always available.
 *
 *******************************************************************************/

/* Includes ------------------------------------------------------------------*/

/* Private includes ----------------------------------------------------------*/
#include "stm32h7xx_hal.h"

#include "app_can.h"
#include "app_crc.h"
#include "app_error.h"
#include "app_flash.h"
#include "app_gpio.h"
#include "app_rtc.h"
#include "app_spi.h"
#include "app_spi_au.h"
#include "app_timers.h"
#include "app_watchdog.h"
#include "app_uart.h"
#include "app_usb.h"
#include "scheduler.h"
#include "sr_bootloader.h"
#include "sr_CAN1.h"
#include "sr_car_net.h"
#include "sr_LED.h"
#include "sr_local_inputs.h"
#include "sr_local_outputs.h"
#include "sr_parameters.h"
#include "sr_SPI_AB.h"
#include "sr_SPI_AU.h"
#include "sr_usb.h"
#include "sr_watchdog.h"
#include "usb_host.h"
#include "fatfs.h"
#include "bootloader_cfg.h"
#include "FRAM.h"
#include "ulog.h"

/* Private typedef -----------------------------------------------------------*/

/* Private define ------------------------------------------------------------*/

/* Private macro -------------------------------------------------------------*/

/* Private variables ---------------------------------------------------------*/
static st_subroutine *pastSubroutines[] = { &gstSR_Watchdog,
                                            &gstSR_CAN1,
                                            &gstSR_Local_Inputs,
                                            &gstSR_Local_Outputs,
                                            &gstSR_SPI_AB,
                                            &gstSR_SPI_AU,
                                            &gstSR_Car_Net,
                                            &gstSR_Bootloader,
                                            &gstSR_LED,
                                            &gstSR_USB,
											&gstSR_BLE_UART
                                            };

static st_subroutine_control stSubroutineControl = { .pastSubroutines = &pastSubroutines,
                                                     .uwNumSubroutines = sizeof(pastSubroutines)
                                                                         / sizeof(pastSubroutines[0]), };

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void PeriphCommonClock_Config(void);

/* Private user code ---------------------------------------------------------*/
int __io_putchar(int ch)
{
	UART4_write((const uint8_t *)&ch, 1);
	return ch;
}

void car_system_logger(ulog_level_t severity, const char *msg)
{
	printf("%.3f [%s]: %s\r\n",
		   Timer_GetCount_1ms() / 1000.0,
		   ulog_level_name(severity),
		   msg);
}

/**
 * @brief  The application entry point.
 * @retval int
 */
int bootloader_main(void)
{
   	/* MCU Configuration--------------------------------------------------------*/
   	/* Reset of all peripherals, Initializes the Flash interface and the Systick. */
   	HAL_Init();

   	/* Configure the system clock */
   	SystemClock_Config();

   	/* Configure the peripherals common clocks */
   	PeriphCommonClock_Config();

   	DMA_Init();
   	UART7_Init();

   	UART4_Init();
   	ULOG_INIT();
#ifdef DEBUG
   	ULOG_SUBSCRIBE((ulog_function_t)&car_system_logger, ULOG_TRACE_LEVEL);
#else
   	ULOG_SUBSCRIBE((ulog_function_t)&car_system_logger, ULOG_INFO_LEVEL);
#endif

   	/* Configure hardware timers used for scheduler and general usec/msec timestamps */
   	Timer2_Init();
   	Timer3_Init();
   	Timer5_Init();

   	// GPIO and CRC already initialized
   	GPIO_Init();
    CRC_Init();

   	/* Configure bootloader specific flash settings */
   	Flash_Init();
    FRAM_Init();

   	/* Initialize all configured peripherals */
   	MX_SPI1_Init();
   	MX_SPI2_Init();
//   	MX_SPI4_Init();
    MX_SPI5_Init();
//    MX_SPI6_Init();
   	SPI_AU_Init();

    /* Initialize USB PSE pin */
    GPIO_WriteOutput(enLOCAL_OUT__USB_PSE, GPIO_PIN_SET);
   	MX_FATFS_Init();
   	MX_USB_HOST_Init();

   	CAN1_Init();

#if IS_NODE(MR_A7_NODE)
   	System_SetNodeID(SYS_NODE__MR_A7);
#elif IS_NODE(CT_A7_NODE)
   	System_SetNodeID(SYS_NODE__CT_A7);
#endif

   	/* Initialize all subroutines */
   	SR_InitializeAllSubroutines(&stSubroutineControl);

   	/* Infinite loop */
   	ULOG_INFO("Initializing bootloader");
   	while(1)
   	{
      	SR_RunSubroutine(&stSubroutineControl);
   	}
}

/**
 * @brief System Clock Configuration
 * @retval None
 */
void SystemClock_Config(void)
{
   RCC_OscInitTypeDef RCC_OscInitStruct = { 0 };
   RCC_ClkInitTypeDef RCC_ClkInitStruct = { 0 };

   /** Supply configuration update enable
    */
   HAL_PWREx_ConfigSupply(PWR_LDO_SUPPLY);

   /** Configure the main internal regulator output voltage
    */
   __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

   while(!__HAL_PWR_GET_FLAG(PWR_FLAG_VOSRDY)) {
   }

   __HAL_RCC_SYSCFG_CLK_ENABLE();
   __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE0);

   while(!__HAL_PWR_GET_FLAG(PWR_FLAG_VOSRDY)) {
   }

   /** Configure LSE Drive Capability
    */
   HAL_PWR_EnableBkUpAccess();
   __HAL_RCC_LSEDRIVE_CONFIG(RCC_LSEDRIVE_HIGH);

   /** Macro to configure the PLL clock source
    */
   __HAL_RCC_PLL_PLLSOURCE_CONFIG(RCC_PLLSOURCE_HSE);

   /** Initializes the RCC Oscillators according to the specified parameters
    * in the RCC_OscInitTypeDef structure.
    */
#if IS_NODE(MR_A7_NODE)
   RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_LSI
                                      | RCC_OSCILLATORTYPE_HSE
                                      | RCC_OSCILLATORTYPE_LSE;
#else
   RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_LSI | RCC_OSCILLATORTYPE_HSE;
#endif
   RCC_OscInitStruct.HSEState = RCC_HSE_BYPASS;
#if IS_NODE(MR_A7_NODE)
   RCC_OscInitStruct.LSEState = RCC_LSE_ON;// 32.678 KHZ
#endif
   RCC_OscInitStruct.LSIState = RCC_LSI_ON;
   RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
   RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;// 25 MHZ
   RCC_OscInitStruct.PLL.PLLM = 5;// PLLM_CLK = HSE_CLK / 5 = 9.6 MHZ -> PPLN multiplier
   RCC_OscInitStruct.PLL.PLLN = 192;// PLLN_CLK = 960 MHZ -> PQR dividers
   RCC_OscInitStruct.PLL.PLLP = 2;// PLLN_CLK/2 = 480 MHZ -> SYSCLK
   RCC_OscInitStruct.PLL.PLLQ = 20;// PLLN_CLK/20 = 48 MHZ -> CAN / USB
   RCC_OscInitStruct.PLL.PLLR = 2;// N/A
   RCC_OscInitStruct.PLL.PLLRGE = RCC_PLL1VCIRANGE_3;
   RCC_OscInitStruct.PLL.PLLVCOSEL = RCC_PLL1VCOWIDE;
   RCC_OscInitStruct.PLL.PLLFRACN = 0;
   if(HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK) {
      Error_Handler();
   }

   /** Initializes the CPU, AHB and APB buses clocks
    */
   RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK
                                 | RCC_CLOCKTYPE_SYSCLK
                                 | RCC_CLOCKTYPE_PCLK1
                                 | RCC_CLOCKTYPE_PCLK2
                                 | RCC_CLOCKTYPE_D3PCLK1
                                 | RCC_CLOCKTYPE_D1PCLK1;
   RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
   RCC_ClkInitStruct.SYSCLKDivider = RCC_SYSCLK_DIV1;// CPU_CLK = 480 MHZ -> CPU_CLK, SYS_TICK_CLK
   RCC_ClkInitStruct.AHBCLKDivider = RCC_HCLK_DIV2;// HCLK = SYS_CLK/2 = 240 MHZ -> CPU2_CLK, SYS_TICK_CLK2, APB/AHB dividers
   RCC_ClkInitStruct.APB3CLKDivider = RCC_APB3_DIV2;// APB3_CLK = HCLK/2 = 120 MHZ
   RCC_ClkInitStruct.APB1CLKDivider = RCC_APB1_DIV2;// APB1_CLK = HCLK/2 = 120 MHZ, -> 240 MHz TIM1_CLK
   RCC_ClkInitStruct.APB2CLKDivider = RCC_APB2_DIV2;// APB2_CLK = HCLK/2 = 120 MHZ, -> 240 MHz TIM2_CLK
   RCC_ClkInitStruct.APB4CLKDivider = RCC_APB4_DIV2;// APB4_CLK = HCLK/2 = 120 MHZ, -> 240 MHz peripheral bus

   if(HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_ACR_LATENCY_7WS) != HAL_OK) {
      Error_Handler();
   }
}

/**
 * @brief Peripherals Common Clock Configuration
 * @retval None
 */
void PeriphCommonClock_Config(void)
{
   RCC_PeriphCLKInitTypeDef PeriphClkInitStruct = { 0 };

   /** Initializes the peripherals clock
    */
   PeriphClkInitStruct.PeriphClockSelection = RCC_PERIPHCLK_SPI1
                                              | RCC_PERIPHCLK_SPI2
                                              | RCC_PERIPHCLK_SPI4
                                              | RCC_PERIPHCLK_SPI5
                                              | RCC_PERIPHCLK_SPI6
                                              | RCC_PERIPHCLK_FDCAN;

   PeriphClkInitStruct.PLL2.PLL2M = 5;
   PeriphClkInitStruct.PLL2.PLL2N = 160;// PLL2N_CLK = 800 MHZ -> PQR divisors
   PeriphClkInitStruct.PLL2.PLL2P = 4;// PLL2P_CLK = PLL2N_CLK/4 = 200 MHz -> ?
   PeriphClkInitStruct.PLL2.PLL2Q = 4;// PLL2Q_CLK = PLL2N_CLK/4 = 200 MHz -> ?
   PeriphClkInitStruct.PLL2.PLL2R = 4;// PLL2R_CLK = PLL2N_CLK/2 = 200 MHz -> QSPI / FMC
   PeriphClkInitStruct.PLL2.PLL2RGE = RCC_PLL2VCIRANGE_3;
   PeriphClkInitStruct.PLL2.PLL2VCOSEL = RCC_PLL2VCOWIDE;
   PeriphClkInitStruct.PLL2.PLL2FRACN = 0;

   PeriphClkInitStruct.PLL3.PLL3M = 5;    // HSE / 5 = 5MHZ
   PeriphClkInitStruct.PLL3.PLL3N = 48;   // PLL3N_CLK = 240 MHZ -> PQR divisors
   PeriphClkInitStruct.PLL3.PLL3P = 3;    // PLL3P_CLK = PLL3N_CLK/3 = 80 MHz -> SPI123
   PeriphClkInitStruct.PLL3.PLL3Q = 15;   // PLL3Q_CLK = PLL3N_CLK/15 = 16 MHz -> SPI456
   PeriphClkInitStruct.PLL3.PLL3R = 7;    // PLL3R_CLK = PLL3N_CLK/7 = 34.285714 MHz -> LTDC (Does it need to be this low?)
   PeriphClkInitStruct.PLL3.PLL3RGE = RCC_PLL3VCIRANGE_3;
   PeriphClkInitStruct.PLL3.PLL3VCOSEL = RCC_PLL3VCOWIDE;
   PeriphClkInitStruct.PLL3.PLL3FRACN = 0;

   PeriphClkInitStruct.Spi123ClockSelection = RCC_SPI123CLKSOURCE_PLL3;// 80 MHz PLL3P (PLL1Q, PLL2P, PLL3P, PER_CK/HSI) -> AB / PLD
   PeriphClkInitStruct.Spi45ClockSelection  = RCC_SPI45CLKSOURCE_PLL3;// 16 MHz PLL3Q (PCLK2, PLL2Q, PLL3Q, HSI) -> FRAM/UI
   PeriphClkInitStruct.Spi6ClockSelection   = RCC_SPI6CLKSOURCE_PLL3;// 80 MHz PLL3Q (PCLK4, PLL2Q, PLL3Q, HSI) -> UI
   PeriphClkInitStruct.FdcanClockSelection  = RCC_FDCANCLKSOURCE_PLL;// PLL1Q = 48 MHZ (HSE, PLL1Q, PLL2Q)

   if(HAL_RCCEx_PeriphCLKConfig(&PeriphClkInitStruct) != HAL_OK) {
      Error_Handler();
   }

   __HAL_FLASH_SET_PROGRAM_DELAY(FLASH_PROGRAMMING_DELAY_3);
}
