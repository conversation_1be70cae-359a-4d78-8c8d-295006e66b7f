/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : app_rtc.h
* @version      : 1.0.0
* @brief Functions for initializing and accessing the real time clock
* @details Functions for initializing and accessing the real time clock
*****************************************************************************/
#ifndef _RTC_H_
#define _RTC_H_

/******************************************************************************
* Includes
*******************************************************************************/
#include <stdint.h>
#include "stm32h7xx_hal.h"

/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/

/******************************************************************************
* Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variables
*******************************************************************************/

/******************************************************************************
* Function Prototypes
*******************************************************************************/
void RTC_Init(void);

void RTC_SetTime(RTC_TimeTypeDef *pstTime);
void RTC_SetDate(RTC_DateTypeDef *pstDate);

void RTC_GetTime(RTC_TimeTypeDef *pstTime);
void RTC_GetDate(RTC_DateTypeDef *pstDate);

#endif /* _RTC_H_ */
