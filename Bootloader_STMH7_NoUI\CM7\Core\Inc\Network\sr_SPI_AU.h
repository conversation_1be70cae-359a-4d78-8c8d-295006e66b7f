/*******************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file           : sr_SPI_AU.h
 * @version 		  : 1.0.0
 * @brief This subroutine loads/unloads data for transmission on SPI which connects the MCUA to UI
 * @details This subroutine loads/unloads data for transmission on SPI which connects the MCUA to UI
 *****************************************************************************/
#ifndef _SR_SPI_AU_
#define _SR_SPI_AU_

/******************************************************************************
 * Includes
 *******************************************************************************/
#include <stdint.h>
#include "app_can.h"
#include "scheduler.h"
#include "system.h"
/******************************************************************************
 * Preprocessor Constants
 *******************************************************************************/

/******************************************************************************
 * Configuration Constants
 *******************************************************************************/

/******************************************************************************
 * Macros
 *******************************************************************************/

/******************************************************************************
 * Typedefs
 *******************************************************************************/

/******************************************************************************
 * Global Variables
 *******************************************************************************/
extern st_subroutine gstSR_SPI_AU;
/******************************************************************************
 * Function Prototypes
 *******************************************************************************/
//en_pass_fail SPI_AU_TransmitDatagram(st_CAN_msg *pstMsg);
en_pass_fail SPI_AU_TransmitDatagram_v1(st_CAN_msg *pstMsg);
en_pass_fail SPI_AU_TransmitDatagram(st_SPI_msg *pstMsg);

uint8_t SPI_AU_CheckIfCommLoss(void);

#endif /* _SR_SPI_AU_ */
