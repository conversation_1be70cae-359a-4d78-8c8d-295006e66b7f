/****************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : app_crc.c
* @version 		  : 1.0.0
* @brief 		  :Functions for initializing and update the CRC engine
* @details 		  :Functions for initializing and update the CRC engine
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include "stm32h7xx_hal.h"
#include "main.h"
/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/

/******************************************************************************
* Module Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variable Definitions
*******************************************************************************/
CRC_HandleTypeDef hcrc;
/******************************************************************************
* Module Variable Definitions
*******************************************************************************/

/******************************************************************************
* Function Prototypes
*******************************************************************************/

/******************************************************************************
* Function Definitions
*******************************************************************************/

/* @fn void CRC_Init(void)
 * @brief Initializes CRC peripheral
 * @param None
 * @return None
 */
void CRC_Init(void)
{
   hcrc.Instance = CRC;
   hcrc.Init.DefaultPolynomialUse = DEFAULT_POLYNOMIAL_ENABLE;
   hcrc.Init.DefaultInitValueUse = DEFAULT_INIT_VALUE_ENABLE;
   hcrc.Init.InputDataInversionMode = CRC_INPUTDATA_INVERSION_NONE;
   hcrc.Init.OutputDataInversionMode = CRC_OUTPUTDATA_INVERSION_DISABLE;
   hcrc.InputDataFormat = CRC_INPUTDATA_FORMAT_WORDS;
   if (HAL_CRC_Init(&hcrc) != HAL_OK)
   {
     Error_Handler();
   }
}

/* @fn void CRC_DeInit(void)
 * @brief Deinitializes CRC peripheral
 * @param None
 * @return None
 */
void CRC_DeInit(void)
{
  HAL_CRC_MspDeInit(&hcrc);
}

/* @fn uint32_t CRC_Accumulate(uint32_t pBuffer[], uint32_t BufferLength)
 * @brief Returns the 32-bit CRC without reinitializing accumulated value, based on HAL_CRC_Accumulate
 * @param None
 * @return Returns the 32-bit CRC without reinitializing accumulated value, based on HAL_CRC_Accumulate
 */
uint32_t CRC_Accumulate(uint32_t pBuffer[], uint32_t BufferLength)
{
   return HAL_CRC_Accumulate(&hcrc, pBuffer, BufferLength);
}

/* @fn uint32_t CRC_Calculate(uint32_t pBuffer[], uint32_t BufferLength)
 * @brief Returns the 32-bit CRC after reinitializing accumulated value, based on HAL_CRC_Accumulate
 * @param None
 * @return Returns the 32-bit CRC after reinitializing accumulated value, based on HAL_CRC_Accumulate
 */
uint32_t CRC_Calculate(uint32_t pBuffer[], uint32_t BufferLength)
{
//   CRC_Init();
   return HAL_CRC_Calculate(&hcrc, pBuffer, BufferLength);
}

/**
* @brief CRC MSP Initialization
* This function configures the hardware resources used in this example
* @param hcrc: CRC handle pointer
* @retval None
*/
void HAL_CRC_MspInit(CRC_HandleTypeDef* hcrc)
{
  if(hcrc->Instance==CRC)
  {
  /* USER CODE BEGIN CRC_MspInit 0 */

  /* USER CODE END CRC_MspInit 0 */
    /* Peripheral clock enable */
    __HAL_RCC_CRC_CLK_ENABLE();
  /* USER CODE BEGIN CRC_MspInit 1 */

  /* USER CODE END CRC_MspInit 1 */
  }

}

/**
* @brief CRC MSP De-Initialization
* This function freeze the hardware resources used in this example
* @param hcrc: CRC handle pointer
* @retval None
*/
void HAL_CRC_MspDeInit(CRC_HandleTypeDef* hcrc)
{
  if(hcrc->Instance==CRC)
  {
  /* USER CODE BEGIN CRC_MspDeInit 0 */

  /* USER CODE END CRC_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_CRC_CLK_DISABLE();
  /* USER CODE BEGIN CRC_MspDeInit 1 */

  /* USER CODE END CRC_MspDeInit 1 */
  }

}
