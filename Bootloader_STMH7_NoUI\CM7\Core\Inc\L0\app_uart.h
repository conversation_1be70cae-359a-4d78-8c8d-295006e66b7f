/*******************************************************************************
 * @Copyright (C) 2025 by Vantage Elevation
 * @file           : app_uart.h
 * @version        : 1.0.1
 * @brief          : UART interface declarations
 * @details        : Header file defining UART4 and UART7 initialization, I/O
 *                   functions, and DMA-based communication APIs.
 *
 * @change-log     :
 *   07/01/2025, [1.0.0] : [*] Initial version with UART4 and UART7 APIs and
 *                             configuration defines.
 *   07/11/2025, [1.0.1] : [~] Updated UART7_BAUDRATE from 115200 to 1000000.
 ******************************************************************************/

#ifndef INC_L0_APP_UART_H_
#define INC_L0_APP_UART_H_

#include "main.h"

#define UART4_BAUDRATE			115200
#define UART4_WRITE_TIMEOUT		0xFFFFFFFF
#define UART4_READ_TIMEOUT		0xFFFFFFFF

#define UART7_BAUDRATE        1000000
#define UART7_WRITE_TIMEOUT   0xFFFFFFFF
#define UART7_READ_TIMEOUT    0xFFFFFFFF

void UART4_Init(void);
void UART4_DeInit(void);
void UART4_write(uint8_t const * const data, uint32_t size);
void UART4_read(uint8_t * data, uint32_t size);

void UART7_Init(void);
void UART7_DeInit(void);
void UART7_write(uint8_t const * const data, uint32_t size);
void UART7_read(uint8_t * data, uint32_t size);

void UART7_write_DMA(uint8_t *data, uint32_t size);
void UART7_read_DMA(uint8_t *data, uint32_t size);
void UART7_read_Idle_DMA(uint8_t *data, uint32_t size);

void DMA_Init(void);

#endif /* INC_L0_APP_UART_H_ */
