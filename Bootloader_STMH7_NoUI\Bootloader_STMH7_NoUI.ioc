#MicroXplorer Configuration settings - do not modify
CAD.formats=[]
CAD.pinconfig=Project naming
CAD.provider=
CRC.IPParameters=InputDataFormat
CRC.InputDataFormat=CRC_INPUTDATA_FORMAT_WORDS
CortexM4.IPs=BDMA,CORTEX_M4\:I,DEBUG,DMA,FATFS_M4\:I,FREERTOS_M4\:I,GPIO,IWDG2\:I,MDMA,NVIC2\:I,OPENAMP_M4\:I,PDM2PCM_M4\:I,PWR,RCC,RESMGR_UTILITY,SYS_M4\:I,USB_DEVICE_M4\:I,USB_HOST_M4\:I,VREFBUF,WWDG2\:I,TIM2,TIM3,TIM5,TIM1,SPI1,SPI2,CRC,FDCAN1,SPI6,SPI5,I2C2
CortexM7.IPs=BDMA\:I,CORTEX_M7\:I,DEBUG\:I,DMA\:I,FATFS_M7\:I,FREERTOS_M7\:I,<PERSON>IO\:I,IWDG1\:I,MDMA\:I,NVIC1\:I,OPENAMP_M7\:I,PDM2PCM_M7\:I,PWR\:I,RCC\:I,RESMGR_UTILITY\:I,SYS\:I,USB_DEVICE_M7\:I,USB_HOST_M7\:I,VREFBUF\:I,WWDG1\:I,TIM3\:I,TIM5\:I,TIM2\:I,TIM1\:I,SPI1\:I,SPI2\:I,CRC\:I,FDCAN1\:I,SPI6\:I,SPI5\:I,USB_OTG_FS\:I,I2C2\:I
FATFS_M7.IPParameters=_USE_LFN,_MAX_SS,_FS_EXFAT
FATFS_M7._FS_EXFAT=1
FATFS_M7._MAX_SS=4096
FATFS_M7._USE_LFN=1
FDCAN1.CalculateBaudRateNominal=600000
FDCAN1.CalculateTimeBitNominal=1666
FDCAN1.CalculateTimeQuantumNominal=333.3333333333333
FDCAN1.FrameFormat=FDCAN_FRAME_FD_NO_BRS
FDCAN1.IPParameters=CalculateTimeQuantumNominal,CalculateTimeBitNominal,CalculateBaudRateNominal,FrameFormat
File.Version=6
GPIO.groupedBy=Group By Peripherals
I2C2.IPParameters=Timing
I2C2.Timing=0x307075B1
KeepUserPlacement=false
Mcu.CPN=STM32H745BIT3
Mcu.Context0=CortexM7
Mcu.Context1=CortexM4
Mcu.ContextNb=2
Mcu.Family=STM32H7
Mcu.IP0=CORTEX_M4
Mcu.IP1=CORTEX_M7
Mcu.IP10=SPI2
Mcu.IP11=SPI5
Mcu.IP12=SPI6
Mcu.IP13=SYS_M4
Mcu.IP14=SYS
Mcu.IP15=TIM1
Mcu.IP16=USB_HOST_M7
Mcu.IP17=USB_OTG_FS
Mcu.IP2=CRC
Mcu.IP3=FATFS_M7
Mcu.IP4=FDCAN1
Mcu.IP5=I2C2
Mcu.IP6=NVIC1
Mcu.IP7=NVIC2
Mcu.IP8=RCC
Mcu.IP9=SPI1
Mcu.IPNb=18
Mcu.Name=STM32H745BITx
Mcu.Package=LQFP208
Mcu.Pin0=PF0
Mcu.Pin1=PF1
Mcu.Pin10=PB15
Mcu.Pin11=PD8
Mcu.Pin12=PD9
Mcu.Pin13=PD11
Mcu.Pin14=PJ10
Mcu.Pin15=PK0
Mcu.Pin16=PK1
Mcu.Pin17=PG8
Mcu.Pin18=PA11
Mcu.Pin19=PA12
Mcu.Pin2=PH0-OSC_IN (PH0)
Mcu.Pin20=PD0
Mcu.Pin21=PD1
Mcu.Pin22=PD7
Mcu.Pin23=PG9
Mcu.Pin24=PG10
Mcu.Pin25=PG11
Mcu.Pin26=PG13
Mcu.Pin27=PG14
Mcu.Pin28=VP_CRC_VS_CRC
Mcu.Pin29=VP_FATFS_M7_VS_USB
Mcu.Pin3=PE11
Mcu.Pin30=VP_SYS_VS_Systick
Mcu.Pin31=VP_SYS_M4_VS_Systick
Mcu.Pin32=VP_TIM1_VS_ControllerModeReset
Mcu.Pin33=VP_TIM1_VS_ClockSourceINT
Mcu.Pin34=VP_TIM1_VS_ClockSourceITR
Mcu.Pin35=VP_USB_HOST_M7_VS_USB_HOST_MSC_FS
Mcu.Pin4=PE12
Mcu.Pin5=PE13
Mcu.Pin6=PE14
Mcu.Pin7=PB12
Mcu.Pin8=PB13
Mcu.Pin9=PB14
Mcu.PinsNb=36
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32H745BITx
MxCube.Version=6.8.0
MxDb.Version=DB.6.0.80
NVIC1.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC1.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC1.ForceEnableDMAVector=true
NVIC1.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC1.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC1.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC1.OTG_FS_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC1.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC1.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC1.SPI1_IRQn=true\:0\:0\:false\:false\:false\:true\:true\:true
NVIC1.SPI2_IRQn=true\:1\:0\:true\:false\:false\:true\:true\:true
NVIC1.SPI5_IRQn=true\:3\:0\:true\:false\:false\:true\:true\:true
NVIC1.SPI6_IRQn=true\:2\:0\:true\:false\:false\:true\:true\:true
NVIC1.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC1.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC1.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC2.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC2.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC2.ForceEnableDMAVector=true
NVIC2.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC2.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC2.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC2.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC2.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC2.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC2.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC2.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA11.GPIOParameters=PinAttribute
PA11.Mode=Host_Only
PA11.PinAttribute=CortexM7
PA11.Signal=USB_OTG_FS_DM
PA12.GPIOParameters=PinAttribute
PA12.Mode=Host_Only
PA12.PinAttribute=CortexM7
PA12.Signal=USB_OTG_FS_DP
PB12.GPIOParameters=GPIO_Speed,PinAttribute
PB12.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PB12.Mode=NSS_Signal_Hard_Input
PB12.PinAttribute=CortexM7
PB12.Signal=SPI2_NSS
PB13.GPIOParameters=GPIO_Speed,PinAttribute
PB13.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PB13.Locked=true
PB13.Mode=RX_Only_Simplex_Unidirect_Slave
PB13.PinAttribute=CortexM7
PB13.Signal=SPI2_SCK
PB14.GPIOParameters=PinAttribute
PB14.Locked=true
PB14.PinAttribute=CortexM7
PB14.Signal=SPI2_MISO
PB15.GPIOParameters=GPIO_Speed,PinAttribute
PB15.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PB15.Locked=true
PB15.Mode=RX_Only_Simplex_Unidirect_Slave
PB15.PinAttribute=CortexM7
PB15.Signal=SPI2_MOSI
PD0.GPIOParameters=PinAttribute
PD0.Locked=true
PD0.Mode=FDCAN_Activate
PD0.PinAttribute=CortexM7
PD0.Signal=FDCAN1_RX
PD1.GPIOParameters=PinAttribute
PD1.Locked=true
PD1.Mode=FDCAN_Activate
PD1.PinAttribute=CortexM7
PD1.Signal=FDCAN1_TX
PD11.Locked=true
PD11.Signal=GPIO_Output
PD7.GPIOParameters=GPIO_Speed,PinAttribute
PD7.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PD7.Locked=true
PD7.Mode=TX_Only_Simplex_Unidirect_Master
PD7.PinAttribute=CortexM7
PD7.Signal=SPI1_MOSI
PD8.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,PinAttribute
PD8.GPIO_Label=GREEN_LED
PD8.GPIO_PuPd=GPIO_PULLUP
PD8.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PD8.Locked=true
PD8.PinAttribute=Free
PD8.Signal=GPIO_Output
PD9.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,PinAttribute
PD9.GPIO_Label=RED_LED
PD9.GPIO_PuPd=GPIO_PULLUP
PD9.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PD9.Locked=true
PD9.PinAttribute=Free
PD9.Signal=GPIO_Output
PE11.GPIOParameters=GPIO_Speed,PinAttribute
PE11.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PE11.Locked=true
PE11.PinAttribute=CortexM7
PE11.Signal=SPI4_NSS
PE12.GPIOParameters=GPIO_Speed,PinAttribute
PE12.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PE12.Locked=true
PE12.PinAttribute=CortexM7
PE12.Signal=SPI4_SCK
PE13.GPIOParameters=GPIO_Speed,PinAttribute
PE13.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PE13.Locked=true
PE13.PinAttribute=CortexM7
PE13.Signal=SPI4_MISO
PE14.GPIOParameters=GPIO_Speed,PinAttribute
PE14.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PE14.Locked=true
PE14.PinAttribute=CortexM7
PE14.Signal=SPI4_MOSI
PF0.GPIOParameters=PinAttribute
PF0.Mode=I2C
PF0.PinAttribute=CortexM7
PF0.Signal=I2C2_SDA
PF1.GPIOParameters=PinAttribute
PF1.Mode=I2C
PF1.PinAttribute=CortexM7
PF1.Signal=I2C2_SCL
PG10.GPIOParameters=GPIO_Speed,PinAttribute
PG10.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PG10.Locked=true
PG10.Mode=NSS_Signal_Hard_Output
PG10.PinAttribute=CortexM7
PG10.Signal=SPI1_NSS
PG11.GPIOParameters=GPIO_Speed,PinAttribute
PG11.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PG11.Locked=true
PG11.Mode=TX_Only_Simplex_Unidirect_Master
PG11.PinAttribute=CortexM7
PG11.Signal=SPI1_SCK
PG13.GPIOParameters=GPIO_Speed,PinAttribute
PG13.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PG13.Locked=true
PG13.Mode=TX_Only_Simplex_Unidirect_Master
PG13.PinAttribute=CortexM7
PG13.Signal=SPI6_SCK
PG14.GPIOParameters=GPIO_Speed,PinAttribute
PG14.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PG14.Locked=true
PG14.Mode=TX_Only_Simplex_Unidirect_Master
PG14.PinAttribute=CortexM7
PG14.Signal=SPI6_MOSI
PG8.GPIOParameters=GPIO_Speed,PinAttribute
PG8.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PG8.Locked=true
PG8.Mode=NSS_Signal_Hard_Output
PG8.PinAttribute=CortexM7
PG8.Signal=SPI6_NSS
PG9.GPIOParameters=GPIO_Speed,PinAttribute
PG9.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PG9.Locked=true
PG9.PinAttribute=CortexM7
PG9.Signal=SPI1_MISO
PH0-OSC_IN\ (PH0).GPIOParameters=PinAttribute
PH0-OSC_IN\ (PH0).Mode=HSE-External-Clock-Source
PH0-OSC_IN\ (PH0).PinAttribute=CortexM7
PH0-OSC_IN\ (PH0).Signal=RCC_OSC_IN
PJ10.GPIOParameters=GPIO_Speed,PinAttribute
PJ10.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PJ10.Locked=true
PJ10.Mode=RX_Only_Simplex_Unidirect_Slave
PJ10.PinAttribute=CortexM7
PJ10.Signal=SPI5_MOSI
PK0.GPIOParameters=GPIO_Speed,PinAttribute
PK0.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PK0.Locked=true
PK0.Mode=RX_Only_Simplex_Unidirect_Slave
PK0.PinAttribute=CortexM7
PK0.Signal=SPI5_SCK
PK1.GPIOParameters=GPIO_Speed,PinAttribute
PK1.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PK1.Locked=true
PK1.Mode=NSS_Signal_Hard_Input
PK1.PinAttribute=CortexM7
PK1.Signal=SPI5_NSS
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.BootMode=boot0
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32H745BITx
ProjectManager.FirmwarePackage=STM32Cube FW_H7 V1.11.0
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=M4-0x400,M7-0x400
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=STM32CubeIDE
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=Bootloader_STM32H7_NoUI.ioc
ProjectManager.ProjectName=Bootloader_STM32H7_NoUI
ProjectManager.ProjectStructure=M7\:Non Secure Project\:true;M4\:Non Secure Project\:true;
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=M4-0x1000,M7-0x1000
ProjectManager.TargetToolchain=STM32CubeIDE
ProjectManager.ToolChainLocation=
ProjectManager.UnderRoot=true
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false-CortexM7,false-2-MX_GPIO_Init-GPIO-true-HAL-true-CortexM7,false-3-MX_TIM1_Init-TIM1-true-HAL-true-CortexM7,4-MX_SPI1_Init-SPI1-false-HAL-true-CortexM7,5-MX_SPI2_Init-SPI2-false-HAL-true-CortexM7,false-6-MX_CRC_Init-CRC-true-HAL-true-CortexM7,false-7-MX_FDCAN1_Init-FDCAN1-true-HAL-true-CortexM7,8-MX_SPI5_Init-SPI5-false-HAL-true-CortexM7,9-MX_SPI6_Init-SPI6-false-HAL-true-CortexM7,10-MX_FATFS_Init-FATFS_M7-false-HAL-false-CortexM7,11-MX_I2C2_Init-I2C2-false-HAL-true-CortexM7,12-MX_USB_HOST_Init-USB_HOST_M7-false-HAL-false-CortexM7,false-1-MX_TIM1_Init-TIM1-false-HAL-false-CortexM4,2-MX_SPI1_Init-SPI1-false-HAL-false-CortexM4,3-MX_SPI2_Init-SPI2-false-HAL-false-CortexM4,false-4-MX_CRC_Init-CRC-true-HAL-false-CortexM4,false-5-MX_FDCAN1_Init-FDCAN1-true-HAL-false-CortexM4,6-MX_SPI5_Init-SPI5-false-HAL-false-CortexM4,7-MX_SPI6_Init-SPI6-false-HAL-false-CortexM4,8-MX_I2C2_Init-I2C2-true-HAL-false-CortexM4,0-MX_CORTEX_M7_Init-CORTEX_M7-false-HAL-true-CortexM7,0-MX_CORTEX_M4_Init-CORTEX_M4-false-HAL-true-CortexM4
RCC.ADCFreq_Value=60000000
RCC.AHB12Freq_Value=240000000
RCC.AHB4Freq_Value=240000000
RCC.APB1Freq_Value=120000000
RCC.APB2Freq_Value=120000000
RCC.APB3Freq_Value=120000000
RCC.APB4Freq_Value=120000000
RCC.AXIClockFreq_Value=240000000
RCC.CECFreq_Value=32000
RCC.CKPERFreq_Value=64000000
RCC.CPU2Freq_Value=240000000
RCC.CPU2SystikFreq_Value=240000000
RCC.CortexFreq_Value=480000000
RCC.CpuClockFreq_Value=480000000
RCC.D1CPREFreq_Value=480000000
RCC.D1PPRE=RCC_APB3_DIV2
RCC.D2PPRE1=RCC_APB1_DIV2
RCC.D2PPRE2=RCC_APB2_DIV2
RCC.D3PPRE=RCC_APB4_DIV2
RCC.DFSDMACLkFreq_Value=48000000
RCC.DFSDMFreq_Value=120000000
RCC.DIVM1=5
RCC.DIVM2=5
RCC.DIVM3=5
RCC.DIVN1=192
RCC.DIVN2=60
RCC.DIVN3=60
RCC.DIVP1Freq_Value=480000000
RCC.DIVP2=5
RCC.DIVP2Freq_Value=60000000
RCC.DIVP3Freq_Value=150000000
RCC.DIVQ1=20
RCC.DIVQ1Freq_Value=48000000
RCC.DIVQ2=3
RCC.DIVQ2Freq_Value=100000000
RCC.DIVQ3=5
RCC.DIVQ3Freq_Value=60000000
RCC.DIVR1Freq_Value=480000000
RCC.DIVR2Freq_Value=150000000
RCC.DIVR3Freq_Value=150000000
RCC.FDCANFreq_Value=48000000
RCC.FMCFreq_Value=240000000
RCC.FamilyName=M
RCC.HCLK3ClockFreq_Value=240000000
RCC.HCLKFreq_Value=240000000
RCC.HPRE=RCC_HCLK_DIV2
RCC.HRTIMFreq_Value=240000000
RCC.I2C123Freq_Value=120000000
RCC.I2C4Freq_Value=120000000
RCC.IPParameters=ADCFreq_Value,AHB12Freq_Value,AHB4Freq_Value,APB1Freq_Value,APB2Freq_Value,APB3Freq_Value,APB4Freq_Value,AXIClockFreq_Value,CECFreq_Value,CKPERFreq_Value,CPU2Freq_Value,CPU2SystikFreq_Value,CortexFreq_Value,CpuClockFreq_Value,D1CPREFreq_Value,D1PPRE,D2PPRE1,D2PPRE2,D3PPRE,DFSDMACLkFreq_Value,DFSDMFreq_Value,DIVM1,DIVM2,DIVM3,DIVN1,DIVN2,DIVN3,DIVP1Freq_Value,DIVP2,DIVP2Freq_Value,DIVP3Freq_Value,DIVQ1,DIVQ1Freq_Value,DIVQ2,DIVQ2Freq_Value,DIVQ3,DIVQ3Freq_Value,DIVR1Freq_Value,DIVR2Freq_Value,DIVR3Freq_Value,FDCANFreq_Value,FMCFreq_Value,FamilyName,HCLK3ClockFreq_Value,HCLKFreq_Value,HPRE,HRTIMFreq_Value,I2C123Freq_Value,I2C4Freq_Value,LPTIM1Freq_Value,LPTIM2Freq_Value,LPTIM345Freq_Value,LPUART1Freq_Value,LTDCFreq_Value,MCO1PinFreq_Value,MCO2PinFreq_Value,PLLSourceVirtual,PWR_Regulator_Voltage_Scale,ProductRev,QSPIFreq_Value,RNGFreq_Value,RTCFreq_Value,SAI1Freq_Value,SAI23Freq_Value,SAI4AFreq_Value,SAI4BFreq_Value,SDMMCFreq_Value,SPDIFRXFreq_Value,SPI123CLockSelection,SPI123Freq_Value,SPI45Freq_Value,SPI6CLockSelection,SPI6Freq_Value,SWPMI1Freq_Value,SYSCLKFreq_VALUE,SYSCLKSource,Spi45ClockSelection,SupplySource,Tim1OutputFreq_Value,Tim2OutputFreq_Value,TraceFreq_Value,USART16Freq_Value,USART234578Freq_Value,USBFreq_Value,VCO1OutputFreq_Value,VCO2OutputFreq_Value,VCO3OutputFreq_Value,VCOInput1Freq_Value,VCOInput2Freq_Value,VCOInput3Freq_Value
RCC.LPTIM1Freq_Value=120000000
RCC.LPTIM2Freq_Value=120000000
RCC.LPTIM345Freq_Value=120000000
RCC.LPUART1Freq_Value=120000000
RCC.LTDCFreq_Value=150000000
RCC.MCO1PinFreq_Value=64000000
RCC.MCO2PinFreq_Value=480000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.PWR_Regulator_Voltage_Scale=PWR_REGULATOR_VOLTAGE_SCALE0
RCC.ProductRev=revV
RCC.QSPIFreq_Value=240000000
RCC.RNGFreq_Value=48000000
RCC.RTCFreq_Value=32000
RCC.SAI1Freq_Value=48000000
RCC.SAI23Freq_Value=48000000
RCC.SAI4AFreq_Value=48000000
RCC.SAI4BFreq_Value=48000000
RCC.SDMMCFreq_Value=48000000
RCC.SPDIFRXFreq_Value=48000000
RCC.SPI123CLockSelection=RCC_SPI123CLKSOURCE_PLL2
RCC.SPI123Freq_Value=60000000
RCC.SPI45Freq_Value=100000000
RCC.SPI6CLockSelection=RCC_SPI6CLKSOURCE_PLL3
RCC.SPI6Freq_Value=60000000
RCC.SWPMI1Freq_Value=120000000
RCC.SYSCLKFreq_VALUE=480000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.Spi45ClockSelection=RCC_SPI45CLKSOURCE_PLL2
RCC.SupplySource=PWR_LDO_SUPPLY
RCC.Tim1OutputFreq_Value=240000000
RCC.Tim2OutputFreq_Value=240000000
RCC.TraceFreq_Value=64000000
RCC.USART16Freq_Value=120000000
RCC.USART234578Freq_Value=120000000
RCC.USBFreq_Value=48000000
RCC.VCO1OutputFreq_Value=960000000
RCC.VCO2OutputFreq_Value=300000000
RCC.VCO3OutputFreq_Value=300000000
RCC.VCOInput1Freq_Value=5000000
RCC.VCOInput2Freq_Value=5000000
RCC.VCOInput3Freq_Value=5000000
SPI1.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_8
SPI1.CalculateBaudRate=7.5 MBits/s
SPI1.DataSize=SPI_DATASIZE_16BIT
SPI1.Direction=SPI_DIRECTION_2LINES_TXONLY
SPI1.IPParameters=VirtualType,Mode,Direction,BaudRatePrescaler,CalculateBaudRate,VirtualNSS,DataSize,NSSPMode,MasterKeepIOState
SPI1.MasterKeepIOState=SPI_MASTER_KEEP_IO_STATE_ENABLE
SPI1.Mode=SPI_MODE_MASTER
SPI1.NSSPMode=SPI_NSS_PULSE_DISABLE
SPI1.VirtualNSS=VM_NSSHARD
SPI1.VirtualType=VM_MASTER
SPI2.DataSize=SPI_DATASIZE_16BIT
SPI2.Direction=SPI_DIRECTION_2LINES_RXONLY
SPI2.IOSwap=SPI_IO_SWAP_DISABLE
SPI2.IPParameters=VirtualType,Mode,Direction,VirtualNSS,DataSize,MasterKeepIOState,IOSwap
SPI2.MasterKeepIOState=SPI_MASTER_KEEP_IO_STATE_DISABLE
SPI2.Mode=SPI_MODE_SLAVE
SPI2.VirtualNSS=VM_NSSHARD
SPI2.VirtualType=VM_SLAVE
SPI5.DataSize=SPI_DATASIZE_16BIT
SPI5.Direction=SPI_DIRECTION_2LINES_RXONLY
SPI5.IPParameters=VirtualType,Mode,Direction,VirtualNSS,DataSize
SPI5.Mode=SPI_MODE_SLAVE
SPI5.VirtualNSS=VM_NSSHARD
SPI5.VirtualType=VM_SLAVE
SPI6.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_8
SPI6.CalculateBaudRate=7.5 MBits/s
SPI6.DataSize=SPI_DATASIZE_16BIT
SPI6.Direction=SPI_DIRECTION_2LINES_TXONLY
SPI6.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate,VirtualNSS,DataSize,BaudRatePrescaler,NSSPMode,MasterKeepIOState
SPI6.MasterKeepIOState=SPI_MASTER_KEEP_IO_STATE_ENABLE
SPI6.Mode=SPI_MODE_MASTER
SPI6.NSSPMode=SPI_NSS_PULSE_DISABLE
SPI6.VirtualNSS=VM_NSSHARD
SPI6.VirtualType=VM_MASTER
SYS.userName=SYS_M7
USB_HOST.BSP.number=1
USB_HOST0.BSP.STBoard=false
USB_HOST0.BSP.api=Unknown
USB_HOST0.BSP.component=
USB_HOST0.BSP.condition=
USB_HOST0.BSP.instance=PD11
USB_HOST0.BSP.ip=GPIO
USB_HOST0.BSP.mode=Output
USB_HOST0.BSP.name=Drive_VBUS_FS
USB_HOST0.BSP.semaphore=
USB_HOST0.BSP.solution=PD11
USB_HOST_M7.IPParameters=USBH_HandleTypeDef-MSC_FS,VirtualModeFS,USBH_DEBUG_LEVEL-MSC_FS
USB_HOST_M7.USBH_DEBUG_LEVEL-MSC_FS=0
USB_HOST_M7.USBH_HandleTypeDef-MSC_FS=hUsbHostFS
USB_HOST_M7.VirtualModeFS=Msc
USB_OTG_FS.IPParameters=VirtualMode,phy_itface
USB_OTG_FS.VirtualMode=Host_Only
USB_OTG_FS.phy_itface=HCD_PHY_EMBEDDED
VP_CRC_VS_CRC.Mode=CRC_Activate
VP_CRC_VS_CRC.Signal=CRC_VS_CRC
VP_FATFS_M7_VS_USB.Mode=USB
VP_FATFS_M7_VS_USB.Signal=FATFS_M7_VS_USB
VP_SYS_M4_VS_Systick.Mode=SysTick
VP_SYS_M4_VS_Systick.Signal=SYS_M4_VS_Systick
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM1_VS_ClockSourceINT.Mode=Internal
VP_TIM1_VS_ClockSourceINT.Signal=TIM1_VS_ClockSourceINT
VP_TIM1_VS_ClockSourceITR.Mode=TriggerSource_ITR0
VP_TIM1_VS_ClockSourceITR.Signal=TIM1_VS_ClockSourceITR
VP_TIM1_VS_ControllerModeReset.Mode=Reset Mode
VP_TIM1_VS_ControllerModeReset.Signal=TIM1_VS_ControllerModeReset
VP_USB_HOST_M7_VS_USB_HOST_MSC_FS.Mode=MSC_FS
VP_USB_HOST_M7_VS_USB_HOST_MSC_FS.Signal=USB_HOST_M7_VS_USB_HOST_MSC_FS
board=custom
isbadioc=false
