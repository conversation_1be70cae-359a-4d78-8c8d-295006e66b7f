/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_local_inputs.h
* @version 		  : 1.0.0
* @brief          : local inputs subroutine
* @details		  : local inputs subroutine
********************************************************************************/
#ifndef _SR_LOCAL_INPUTS_H_
#define _SR_LOCAL_INPUTS_H_
/*******************************************************************************
* Includes
********************************************************************************/
#include <stdint.h>
#include "app.h"
/*******************************************************************************
* Preprocessor Constants
********************************************************************************/


/*******************************************************************************
* Configuration Constants
********************************************************************************/


/*******************************************************************************
* Macros
********************************************************************************/


/*******************************************************************************
* Typedefs
********************************************************************************/


/*******************************************************************************
* Global Variables
********************************************************************************/
extern st_subroutine gstSR_Local_Inputs;

/*******************************************************************************
* Function Prototypes
********************************************************************************/
uint32_t LocalInputs_GetInputBitmap(uint8_t ucArrayIndex);
uint8_t LocalInputs_GetInputValue(en_local_inputs eInput);
void setTestBufFrom_CTA7(uint8_t index, uint8_t value);//AS todo delete
#endif /* _SR_LOCAL_INPUTS_H_ */
