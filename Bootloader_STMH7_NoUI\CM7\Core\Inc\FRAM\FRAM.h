/****************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_FRAM.h
* @version 		  : 1.0.0
* @brief 		  :Subroutine manages communication with the MB85RS4MTY 4Mb SPI FRAM chip
* @details 		:Subroutine manages communication with the MB85RS4MTY 4Mb SPI FRAM chip
*****************************************************************************/
#ifndef _FRAM_H_
#define _FRAM_H_

/******************************************************************************
* Includes
*******************************************************************************/
#include "app.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/

/******************************************************************************
* Macros
*******************************************************************************/

/* MB85RS4MTY Op-Codes */
#define WREN      0x06  // Set write Enable Latch
#define WRDI      0x04  // Reset Write Enable Latch
#define RDSR      0x05  // Read Status Register
#define WRSR      0x01  // Write Status register
#define READ      0x03  // Read Memory Code
#define WRITE     0x02  // Write Memory Code
#define FSTRD     0x0B  // Fast Read Memory Code
#define DPD       0xBA  // Deep Power Down Mode
#define HIBERNATE 0xB9  // HibernateMode
#define RDID      0x9F  // Read Device ID
#define RUID      0x4C  // Read Unique ID
#define WRSN      0xC2  // Write Serial Number
#define RDSN      0xC3  // Read Serial Number
#define SSWR      0x42  // Write Special Sector
#define SSRD      0x4B  // Read Special Sector
#define FSSRD     0x49  // Fast Read Special Sector

/* MB85RS4MTY status register values */
#define WPEN_Pos    7   // Status Register Write Protect
#define BP1_Pos     3   // Block Protect Bit 1
#define BP0_Pos     2   // Block Protect Bit 0
#define WEL_Pos     1   // Write Enable Latch

#define WPEN        (1 << WPEN_Pos)
#define BP0         (1 << BP0_Pos)
#define BP1         (1 << BP1_POS)
#define BP          (3 << BP0_Pos)
#define WEL         (1 << WEL_Pos)

/******************************************************************************
* Function Prototypes
*******************************************************************************/
void FRAM_Init(void);
en_pass_fail FRAM_SetWriteEnableLatch(void);
en_pass_fail FRAM_ResetWriteEnableLatch(void);
en_pass_fail FRAM_ReadStatusRegister(uint8_t *sr_data);
en_pass_fail FRAM_WriteStatusRegister(uint8_t sr_data);
en_pass_fail FRAM_ReadMemoryCode(uint32_t address, uint8_t *data, uint16_t size);
en_pass_fail FRAM_WriteMemoryCode(uint32_t address, uint8_t *data, uint16_t size);
en_pass_fail FRAM_FastReadMemoryCode(uint32_t address, uint8_t *data, uint16_t size);
en_pass_fail FRAM_DeepPowerDownMode(void);
en_pass_fail FRAM_HibernateMode(void);
en_pass_fail FRAM_ReadDeviceID(uint32_t *id);
en_pass_fail FRAM_ReadUniqueID(uint32_t *id);
en_pass_fail FRAM_WriteSerialNumber(void);
en_pass_fail FRAM_ReadSerialNumber(void);
en_pass_fail FRAM_ReadSpecialSector(void);
en_pass_fail FRAM_WriteSpecialSector(void);
en_pass_fail FRAM_FastReadSpecialSector(void);

#endif /* _FRAM_H_ */
