/*******************************************************************************
 * @Copyright (C) 2022 by Vantage Elevation
 * @file         : sr_usb.h
 * @version 		  : 1.0.0
 * @brief        : USB subroutine
 * @details		  : USB subroutine
 ********************************************************************************/
#ifndef _SR_USB_H_
#define _SR_USB_H_
/*******************************************************************************
 * Includes
 ********************************************************************************/
#include <stdint.h>
#include "bootloader_def.h"
#include "scheduler.h"
#include "system.h"

#include "bootloader_usb.h"

extern st_subroutine gstSR_USB;

void USB_ConnectionStateCallback(uint8_t ucConnectionState);

#endif /* _SR_USB_H_ */
