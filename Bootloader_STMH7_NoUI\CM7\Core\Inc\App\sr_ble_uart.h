/*******************************************************************************
 * @Copyright (C) 2022 by Vantage Elevation
 * @file           : sr_ble_uart.h
 * @version        : 1.0.1
 * @brief          : BLE UART subroutine header
 * @details        : Header file defining UART7-based BLE communication interface
 *                   between ESP32 and STM32, including packet types, UART states,
 *                   and key function prototypes for BLE command handling.
 *
 * @change-log     :
 *   07/01/2025, [1.0.0] : [*] Initial version with BLE config read command and
 *                             UART7 packet interface.
 *   07/11/2025, [1.0.1] : [+] Added command type for BLE_CONFIG_WRITE.
 ******************************************************************************/

#ifndef _SR_BLE_UART_H_
#define _SR_BLE_UART_H_

/*******************************************************************************
 * Includes
 ******************************************************************************/
#include <stdint.h>
#include "app.h"

/*******************************************************************************
 * Preprocessor Constants
 ******************************************************************************/
#define UART_CMD_HEADER_SIZE        (6U)               /* Header = Start(1) + Type(1) + Length(4) */
#define UART_CMD_MAX_PAYLOAD_SIZE   (256U * 1024U)     /* Maximum UART payload size = 256 KB */
#define UART_CMD_CRC_SIZE           (4U)               /* CRC32 size (bytes) */
#define UART_START_BYTE             (0xA5U)            /* Start byte for UART packets */
#define UART_DMA_MAX_CHUNK          (65535U)           /* Max DMA chunk size per transfer */

#define CFG_MAX_SIZE                (5400U)            /* Max size of BLE config buffer */
#define MAX_PKT_SIZE                (16384U)           /* Max allowed response payload size */

#define UART_CMD_TYPE_INDEX         (1U)  /* Command type byte */

/*******************************************************************************
 * Typedefs
 ******************************************************************************/

/**
 * @brief Enum defining BLE packet types for BLE-UART communication protocol.
 */
typedef enum
{
    /* 0x01 – 0x0F : General Acknowledgement */
    BLE_ACK                  = 0x01,   /* Generic ACK */
    BLE_NACK                 = 0x02,   /* Generic NACK */

    /* 0x10 – 0x1F : BLE State Control */
    BLE_START_ADV            = 0x10,   /* Start BLE advertising */
    BLE_STOP_ADV             = 0x11,   /* Stop BLE advertising */
    BLE_CONNECTED            = 0x12,   /* BLE connected notification */
    BLE_DISCONNECTED         = 0x13,   /* BLE disconnected notification */

    /* 0x20 – 0x2F : Firmware Upload (Mobile -> ESP32) */
    BLE_FW_UPLOAD_START      = 0x20,   /* Firmware upload initiation */
    BLE_FW_UPLOAD_DATA       = 0x21,   /* Firmware data packet */
    BLE_FW_UPLOAD_END        = 0x22,   /* End of firmware upload */
    BLE_FW_UPLOAD_SUCCESS    = 0x23,   /* Firmware upload success notification */
    BLE_FW_UPLOAD_FAIL       = 0x24,   /* Firmware upload failure notification */

    /* 0x30 – 0x3F : Firmware Chunk Transfer (ESP32 <-> STM32) */
    BLE_FW_CHUNK_START       = 0x30,   /* Start firmware chunk transfer */
    BLE_FW_CHUNK_DATA        = 0x31,   /* Firmware chunk data */
    BLE_FW_CHUNK_FAILED      = 0x32,   /* Chunk transfer failed */
    BLE_FW_CHUNK_END         = 0x33,   /* End of chunk transfer */

    /* 0x50 – 0x5F : Configuration Communication */
    BLE_CONFIG_REQUEST       = 0x50,   /* Mobile requests configuration from STM32 */
    BLE_CONFIG_RESPONSE      = 0x51,   /* STM32 sends configuration to Mobile */
	BLE_CONFIG_WRITE         = 0x56,   /* ESP sends configuration Write Req to Nexus */


    /* 0x60 – 0x6F : Error Reporting */
    BLE_PKT_ERROR            = 0x60    /* Error occurred during any of the process */
} BLE_PacketType_t;

/**
 * @brief UART reception state machine for BLE packet handling.
 */
typedef enum {
    UART_RX_WAIT_HEADER   = 0,  /* Waiting for header bytes */
    UART_RX_WAIT_PAYLOAD  = 1,	/* Waiting for payload data */
    UART_RX_WAIT_CRC      = 2, 	/* Waiting for CRC bytes */
    UART_RX_COMPLETE      = 3, 	/* Reception complete */
	UART_RX_ERROR         = 4, 	/* UART receive Error */
	UART_TX_ERROR         = 5  	/* UART Transmit Error */
} UART_RxState_t;

/*******************************************************************************
 * Global Variables
 ******************************************************************************/
extern st_subroutine gstSR_BLE_UART;       /* Subroutine handle for BLE UART task */
extern UART_HandleTypeDef UART7_Handle;    /* UART7 handle for hardware abstraction */
extern __USB_BUFFER_SECTION uint8_t USBReadBuffer[APP_READ_BUFFER_SIZE];

/*******************************************************************************
 * Function Prototypes
 ******************************************************************************/

/**
 * @brief Start UART reception using idle line detection and DMA.
 */
void UART7_StartReception(void);

/**
 * @brief Continue reception of UART payload data after header is received.
 */
void UART7_ContinuePayloadReception(void);

/**
 * @brief Process received UART command packet.
 */
void Process_UART_Command(uint8_t *, uint32_t);

/**
 * @brief Extract CRC32 value from received 4 CRC bytes.
 */
uint32_t ExtractCRC32(const uint8_t *);

/**
 * @brief Send UART ACK/NACK response based on result.
 */
void UART_SendResponse(uint8_t);

/**
 * @brief Send configuration data to mobile application over BLE.
 */
void SendBLEConfigResponse(void);

/**
 * @brief Read configuration data stored for BLE.
 */
en_pass_fail ReadBLEConfiguration(uint8_t *, uint32_t *);

/**
 * @brief Reflects the lower 'bits' bits of the input value.
 */
uint32_t reverseBitOrder(uint32_t, int);

/**
 * @brief Computes the CRC32 checksum over the input data.
 */
uint32_t crc32(const uint8_t *, uint32_t);

/**
 * @brief Validates the given CRC32 against calculated value.
 */
bool validate_crc(const uint8_t *, uint32_t, uint32_t);

/**
 * @brief Write configuration to the Nexus Board FRAM
 */
en_pass_fail writeConfigurationToNexus(uint8_t *, uint32_t);

/**
 * @brief clears the common buffer
 */
void clear_UART_rx_buffers(void);

#endif /* _SR_BLE_UART_H_ */
