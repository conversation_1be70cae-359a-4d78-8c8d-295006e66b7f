#include "FRAM.h"
#include "app_spi.h"

void FRAM_Init(void)
{
  MX_SPI4_Init();
}

en_pass_fail FRAM_SetWriteEnableLatch(void)
{
  uint8_t op_code = WREN;

  HAL_GPIO_WritePin(GPIOE, GPIO_PIN_11, GPIO_PIN_RESET);

  HAL_StatusTypeDef status = HAL_SPI_Transmit(&hspi4,
      &op_code,
      1,
      100);

  HAL_GPIO_WritePin(GPIOE, GPIO_PIN_11, GPIO_PIN_SET);

  return ((status != HAL_OK) ? FAIL : PASS);
}

en_pass_fail FRAM_ResetWriteEnableLatch(void)
{
  uint8_t op_code = WRDI;

  HAL_GPIO_WritePin(GPIOE, GPIO_PIN_11, GPIO_PIN_RESET);

  HAL_StatusTypeDef status = HAL_SPI_Transmit(&hspi4,
      &op_code,
      1,
      100);

  HAL_GPIO_WritePin(GPIOE, GPIO_PIN_11, GP<PERSON>_PIN_SET);

  return ((status != HAL_OK) ? FAIL : PASS);
}

en_pass_fail FRAM_ReadStatusRegister(uint8_t *sr_data)
{
  uint8_t op_code = RDSR;
  HAL_StatusTypeDef status = HAL_SPI_TransmitReceive(&hspi4,
      &op_code,
      sr_data,
      2,
      100);

  return ((status != HAL_OK) ? FAIL : PASS);
}

en_pass_fail FRAM_WriteStatusRegister(uint8_t sr_data)
{
  uint8_t op_code = WRSR;
  uint8_t data_out[] = {op_code, sr_data};

  HAL_StatusTypeDef status = HAL_SPI_Transmit(&hspi4,
      &data_out[0],
      2,
      100);

  return ((status != HAL_OK) ? FAIL : PASS);
}

en_pass_fail FRAM_ReadMemoryCode(uint32_t address, uint8_t *data,
		uint16_t size)
{
  en_pass_fail result;
  uint8_t data_out[4];
  data_out[0] = READ;
  data_out[1] = (address >> 16) & 0xFF;
  data_out[2] = (address >> 8)  & 0xFF;
  data_out[3] = (address >> 0)  & 0xFF;

  /* Send opcode and address to read from */
  HAL_GPIO_WritePin(GPIOE, GPIO_PIN_11, GPIO_PIN_RESET);

  HAL_StatusTypeDef status = HAL_SPI_Transmit(&hspi4,
      &data_out[0],
      4,
      100);

  if(status == HAL_OK)
  {
    /* Receive data from FRAM */
    status = HAL_SPI_Receive(&hspi4, &data[0], size, 500);
    result = (status == HAL_OK) ? PASS : FAIL;
  }
  else
  {
    result = FAIL;
  }

  HAL_GPIO_WritePin(GPIOE, GPIO_PIN_11, GPIO_PIN_SET);

  return result;
}

en_pass_fail FRAM_WriteMemoryCode(uint32_t address, uint8_t *data,
		uint16_t size)
{
  en_pass_fail result;
  uint8_t data_out[4];
  data_out[0] = WRITE;
  data_out[1] = (address >> 16) & 0xFF;
  data_out[2] = (address >> 8)  & 0xFF;
  data_out[3] = (address >> 0)  & 0xFF;

  /* Send opcode and address to read from */
  HAL_GPIO_WritePin(GPIOE, GPIO_PIN_11, GPIO_PIN_RESET);

  HAL_StatusTypeDef status = HAL_SPI_Transmit(&hspi4,
      &data_out[0],
      4,
      100);

  if(status == HAL_OK)
  {
    /* Transmit data to FRAM */
    status = HAL_SPI_Transmit(&hspi4, data, size, 500);
    result = (status == HAL_OK) ? PASS : FAIL;
  }
  else
  {
    result = FAIL;
  }

  HAL_GPIO_WritePin(GPIOE, GPIO_PIN_11, GPIO_PIN_SET);

  return result;
}

en_pass_fail FRAM_FastReadMemoryCode(uint32_t address, uint8_t *data,
		uint16_t size)
{
  en_pass_fail result;
  uint8_t data_out[5];
  data_out[0] = FSTRD;
  data_out[1] = (address >> 16) & 0xFF;
  data_out[2] = (address >> 8)  & 0xFF;
  data_out[3] = (address >> 0)  & 0xFF;
  data_out[5] = 0x00;

  /* Send opcode and address to read from */
  HAL_StatusTypeDef status = HAL_SPI_Transmit(&hspi4,
      &data_out[0],
      5,
      100);

  if(status == HAL_OK)
  {
    /* Receive data from FRAM */
    status = HAL_SPI_Receive(&hspi4, data, size, 1000);
    result = (status == HAL_OK) ? PASS : FAIL;
  }
  else
  {
    result = FAIL;
  }

  return result;
}

en_pass_fail FRAM_DeepPowerDownMode(void)
{
  uint8_t op_code = DPD;
  HAL_StatusTypeDef status = HAL_SPI_Transmit(&hspi4,
      &op_code,
      1,
      100);

  return ((status != HAL_OK) ? FAIL : PASS);
}

en_pass_fail FRAM_HibernateMode(void)
{
  uint8_t op_code = HIBERNATE;
  HAL_StatusTypeDef status = HAL_SPI_Transmit(&hspi4,
      &op_code,
      1,
      100);

  return ((status != HAL_OK) ? FAIL : PASS);
}

en_pass_fail FRAM_ReadDeviceID(uint32_t *id)
{
  en_pass_fail result;
  uint8_t op_code = RDID;

  /* Send opcode and address to read from */
  HAL_StatusTypeDef status = HAL_SPI_Transmit(&hspi4,
      &op_code,
      1,
      100);

  if(status == HAL_OK)
  {
    /* Receive data from FRAM */
    status = HAL_SPI_Receive(&hspi4, (uint8_t*)id, 4, 1000);
    result = (status == HAL_OK) ? PASS : FAIL;
  }
  else
  {
    result = FAIL;
  }

  return result;
}

en_pass_fail FRAM_ReadUniqueID(uint32_t *id)
{
  en_pass_fail result;
  uint8_t op_code = RUID;

  /* Send opcode and address to read from */
  HAL_StatusTypeDef status = HAL_SPI_Transmit(&hspi4,
      &op_code,
      1,
      100);

  if(status == HAL_OK)
  {
    /* Receive data from FRAM */
    status = HAL_SPI_Receive(&hspi4, (uint8_t*)id, 8, 1000);
    result = (status == HAL_OK) ? PASS : FAIL;
  }
  else
  {
    result = FAIL;
  }

  return result;
}

en_pass_fail FRAM_WriteSerialNumber(void)
{
  return FAIL;
}

en_pass_fail FRAM_ReadSerialNumber(void)
{
  return FAIL;
}

en_pass_fail FRAM_ReadSpecialSector(void)
{
  return FAIL;
}

en_pass_fail FRAM_WriteSpecialSector(void)
{
  return FAIL;
}

en_pass_fail FRAM_FastReadSpecialSector(void)
{
  return FAIL;
}
