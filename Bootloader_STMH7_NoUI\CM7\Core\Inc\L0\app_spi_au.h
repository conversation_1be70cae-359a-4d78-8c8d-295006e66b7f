#ifndef INC_L0_APP_SPI_AU_H_
#define INC_L0_APP_SPI_AU_H_

#include "stm32h7xx_hal.h"
//#include "lwrb/lwrb.h"

#define SPI_AU_RX_BUFFER_SIZE 1024
#define SPI_AU_TX_BUFFER_SIZE 1024

typedef enum
{
  SPI_TX_IDLE,
  SPI_TX_TRANSFER_COMPLETE
}spi_tx_state_t;

typedef struct
{
  SPI_HandleTypeDef *hspi;
  DMA_HandleTypeDef *hdma;
//  lwrb_t ringbuffer;
  uint8_t *buffer;
  uint32_t transmit_size;
  spi_tx_state_t tx_state;
  uint32_t size;
}SPI_device_t;

extern SPI_device_t SPI_AU_TX;
extern SPI_device_t SPI_AU_RX;

void SPI_AU_Init(void);
void SPI_AU_Transmit(uint16_t *data, uint16_t size);
void SPI_AU_TransmitEncoded(uint16_t *data, uint16_t size);

#endif
