/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : app_rtc.c
* @version      : 1.0.0
* @brief Functions for initializing and accessing the real time clock
* @details Functions for initializing and accessing the real time clock
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include "app.h"
#include <stdio.h>// printf
/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/
/* From STM32H7 documentation:
The asynchronous prescaler division factor is set to 128, and the synchronous division
factor to 256, to obtain an internal clock frequency of 1 Hz (ck_spre) with an LSE frequency
of 32.768 kHz.
ck_apre frequency = RTCCLK frequency/(PREDIV_A+1)
ck_spre frequency = ck_apre frequency/(PREDIV_S+1)
  */
#define RTC_EXTERNAL_OSCILLATOR_FREQUENCY_HZ             (32768)
#define RTC_ASYNC_PRESCALE_DIVIDER                       (127)
#define RTC_SYNC_PRESCALE_DIVIDER                        (255)
/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/

/******************************************************************************
* Module Typedefs
*******************************************************************************/
//typedef struct
//{
//   // 19
//   uint8_t ucMonth : 4;
//   uint8_t ucDayOfWeek : 3;
//   uint8_t ucDayOfMonth : 5;
//   uint8_t ucYear : 7;
//
//   // 17
//   uint8_t ucHour : 5;
//   uint8_t ucMinute : 6;
//   uint8_t ucSecond : 6;
//   // 36 + 1 bits
//   uint8_t bDST : 1;
//}test_structs;
/******************************************************************************
* Global Variable Definitions
*******************************************************************************/
RTC_HandleTypeDef hrtc;
/******************************************************************************
* Module Variable Definitions
*******************************************************************************/

/******************************************************************************
* Function Prototypes
*******************************************************************************/

/******************************************************************************
* Function Definitions
*******************************************************************************/

/* @fn void RTC_SetTime(RTC_TimeTypeDef *pstTime)
 * @brief Sets the RTC time. Based on HAL_RTC_SetTime
 * @param pstTime, pointer to time structure RTC_TimeTypeDef
 * @return None
 */
void RTC_SetTime(RTC_TimeTypeDef *pstTime)
{
   if (HAL_RTC_SetTime(&hrtc, pstTime, RTC_FORMAT_BIN) != HAL_OK)
   {
      Error_Handler();
   }


//   uint32_t uiRegister = ( RTC_ByteToBcd2(sTime->Hours)   << RTC_TR_HU_Pos )
//                       | ( RTC_ByteToBcd2(sTime->Minutes) << RTC_TR_MNU_Pos )
//                       | ( RTC_ByteToBcd2(sTime->Seconds) << RTC_TR_SU_Pos );

   /* Enter Initialization mode */
//    status = RTC_EnterInitMode(hrtc);
//    if (status == HAL_OK)

//   /* Set the RTC_TR register */
//   hrtc->Instance->TR = (uint32_t)(uiRegister & RTC_TR_RESERVED_MASK);
//
//   /* Clear the bits to be configured */
//   hrtc->Instance->CR &= ((uint32_t)~RTC_CR_BKP);
//
//   /* Configure the RTC_CR register */
//   hrtc->Instance->CR |= (uint32_t)(pstTime->DayLightSaving);
//
//   /* Exit Initialization mode */
////   status = RTC_ExitInitMode(hrtc);
//
//   /* Disable the write protection for RTC registers */
//   __HAL_RTC_WRITEPROTECTION_DISABLE(&hrtc);
//
//
//   /* Enable the write protection for RTC registers */
//   __HAL_RTC_WRITEPROTECTION_ENABLE(&hrtc);
}
/* @fn void RTC_SetDate(RTC_DateTypeDef *pstDate)
 * @brief Sets the RTC date. Based on HAL_RTC_SetDate
 * @param pstDate, pointer to date structure RTC_DateTypeDef
 * @return None
 */
void RTC_SetDate(RTC_DateTypeDef *pstDate)
{
   if (HAL_RTC_SetDate(&hrtc, pstDate, RTC_FORMAT_BIN) != HAL_OK)
   {
      Error_Handler();
   }
}
/* @fn void RTC_GetTime(RTC_TimeTypeDef *pstTime)
 * @brief Gets the RTC time. Based on HAL_RTC_GetTime
 * @param pstTime, pointer to time structure RTC_TimeTypeDef
 * @return None
 */
void RTC_GetTime(RTC_TimeTypeDef *pstTime)
{
   if(HAL_RTC_GetTime(&hrtc, pstTime, RTC_FORMAT_BIN) != HAL_OK)
   {
      Error_Handler();
   }
}
/* @fn void RTC_GetDate(RTC_DateTypeDef *pstDate)
 * @brief Gets the RTC date. Based on HAL_RTC_GetDate
 * @param pstDate, pointer to date structure RTC_DateTypeDef
 * @return None
 */
void RTC_GetDate(RTC_DateTypeDef *pstDate)
{
   if (HAL_RTC_GetDate(&hrtc, pstDate, RTC_FORMAT_BIN) != HAL_OK)
   {
      Error_Handler();
   }
}

/* @fn void RTC_Init(void)
 * @brief Initializes RTC
 * @param None
 * @return None
 */
void RTC_Init(void)
{
   /* USER CODE BEGIN RTC_Init 0 */
   /* USER CODE END RTC_Init 0 */

   /* USER CODE BEGIN RTC_Init 1 */
//   /*  Enable smooth calibration */
//   HAL_RTCEx_SetSmoothCalib(&hrtc, RTC_SMOOTHCALIB_PERIOD_32SEC, RTC_SMOOTHCALIB_PLUSPULSES_RESET, 0);
   /* USER CODE END RTC_Init 1 */
   /** Initialize RTC Only
   */
   hrtc.Instance = RTC;
   hrtc.Init.HourFormat = RTC_HOURFORMAT_24;
   hrtc.Init.AsynchPrediv = 127;
   hrtc.Init.SynchPrediv = 255;
   hrtc.Init.OutPut = RTC_OUTPUT_DISABLE;
   hrtc.Init.OutPutPolarity = RTC_OUTPUT_POLARITY_HIGH;
   hrtc.Init.OutPutType = RTC_OUTPUT_TYPE_OPENDRAIN;
   hrtc.Init.OutPutRemap = RTC_OUTPUT_REMAP_NONE;
   if (HAL_RTC_Init(&hrtc) != HAL_OK)
   {
     Error_Handler();
   }

   /* USER CODE BEGIN Check_RTC_BKUP */
   if( HAL_RTCEx_BKUPRead(&hrtc, RTC_BKP_DR0) != 0x1234 )
   {
      printf("Backup Lost\n");
      /* Place marker that RTC has been set before */
      HAL_RTCEx_BKUPWrite(&hrtc, RTC_BKP_DR0, 0x1234);
   }
   /* USER CODE END Check_RTC_BKUP */

   /* USER CODE BEGIN RTC_Init 2 */
   /*  Enable smooth calibration */
   /* Bit 15 CALP: Increase frequency of RTC by 488.5 ppm
         0: No RTCCLK pulses are added.
         1: One RTCCLK pulse is effectively inserted every 211 pulses (frequency increased by
         488.5 ppm).
         This feature is intended to be used in conjunction with CALM, which lowers the frequency of
         the calendar with a fine resolution. if the input frequency is 32768 Hz, the number of RTCCLK
         pulses added during a 32-second window is calculated as follows: (512 * CALP) - CALM.
         Refer to Section 49.3.13: RTC smooth digital calibration.
    * */
   /* Bits 8:0 CALM[8:0]: Calibration minus
         The frequency of the calendar is reduced by masking CALM out of 220 RTCCLK pulses (32
         seconds if the input frequency is 32768 Hz). This decreases the frequency of the calendar
         with a resolution of 0.9537 ppm.
         To increase the frequency of the calendar, this feature should be used in conjunction with
         CALP. See Section 49.3.13: RTC smooth digital calibration on page 2090. */
   /* RTC_SMOOTHCALIB_PERIOD_32SEC, CLOCK_CYCLES = 2^20
    * FCAL = FRTCCLK x [1 + (CALP x 512 - CALM) / (CLOCK_CYCLES + CALM - CALP x 512)]
    * FCAL = 32678
    * Current Clock is slow, let FRTCCLK = FCAL * (1+PPM)
    * PPM = (776873-776982)/776982 = -1.4028639016090462842125042793784e-4
    * We want to adjust the calibration so set CALP = 1
    * FCAL/FRTCLK = 1/(1+PPM) = 1+ (512 - CALM)/(2^20 + CALM - 512)
    * -PPM / (1+PPM) = (512 - CALM)/(CLOCK_CYCLES + CALM - 512)
    * CALM = ( 512 - CLOCK_CYCLES * 1.4030607319343058646651383173527e-4 ) /  (1.0001403060731934305864665138317)
    * CALM = (512 - 147.12158100487467063471120762564) / (1.0001403060731934305864665138317) = 364.95025570460036582555964745621
    */
   HAL_RTCEx_SetSmoothCalib(&hrtc, RTC_SMOOTHCALIB_PERIOD_32SEC, RTC_SMOOTHCALIB_PLUSPULSES_SET, 365);

   /* In addition, when it is clocked by the LSE, the RTC keeps on running under system reset if
the reset source is different from the Backup domain reset one (refer to the RTC clock
section of the Reset and clock controller for details on the list of RTC clock sources not
affected by system reset). When a Backup domain reset occurs, the RTC is stopped and all
the RTC registers are set to their reset values */

   /* Power  7.1*/
   int bBREN = (PWR->CR2 >> 0) & 1;
   int bMONEN = (PWR->CR2 >> 4) & 1;
   int bBRRDY = (PWR->CR2 >> 16) & 1;
   int bVBAT = (PWR->CR2 >> 20) & 3;
   int bTEMP = (PWR->CR2 >> 22) & 3;
   int bLSEDRV = (RCC->BDCR >> 3) & 3;
   printf("PWR: BREN %d, MONEN %d, BRRDY %d, VBAT %d, TEMP %d, LSEDRV %d \n", bBREN, bMONEN, bBRRDY, bVBAT, bTEMP, bLSEDRV);

   /* Oscillators 9.7.25*/
   int bBDRST = (RCC->BDCR >> 16) & 1;
   int bRTCEN = (RCC->BDCR >> 15) & 1;
   int bRTCSEL = (RCC->BDCR >> 8) & 3;
   int bLSERDYON = (RCC->BDCR >> 0) & 3;
   printf("RCC: BDRST %d, bRTCEN %d, bRTCSEL %d, LSERDY/LSEON %d \n", bBDRST, bRTCEN, bRTCSEL, bLSERDYON);

   /* LSE status */

   // 7.4.4 Backup domain To retain when VDD is turned off...
   // Backup domain reset 9.4.6
   /*
    * Accessing the backup domain
After reset, the backup domain (RTC registers and RTC backup registers) is protected
against possible unwanted write accesses. To enable access to the backup domain, set the
DBP bit in the PWR control register 1 (PWR_CR1).
For more detail on RTC and backup RAM access, refer to Section
    * */
   /* USER CODE END RTC_Init 2 */

}
/**
* @brief RTC MSP Initialization
* This function configures the hardware resources used in this example
* @param hrtc: RTC handle pointer
* @retval None
*/
void HAL_RTC_MspInit(RTC_HandleTypeDef* hrtc)
{
  if(hrtc->Instance==RTC)
  {
  /* USER CODE BEGIN RTC_MspInit 0 */
    RCC_PeriphCLKInitTypeDef PeriphClkInitStruct = {0};
    PeriphClkInitStruct.PeriphClockSelection = RCC_PERIPHCLK_RTC;
    PeriphClkInitStruct.RTCClockSelection = RCC_RTCCLKSOURCE_LSE;
    if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInitStruct) != HAL_OK)
    {
      Error_Handler();
    }
  /* USER CODE END RTC_MspInit 0 */
    /* Peripheral clock enable */
    __HAL_RCC_RTC_ENABLE();
  /* USER CODE BEGIN RTC_MspInit 1 */

  /* USER CODE END RTC_MspInit 1 */
  }

}

/**
* @brief RTC MSP De-Initialization
* This function freeze the hardware resources used in this example
* @param hrtc: RTC handle pointer
* @retval None
*/
void HAL_RTC_MspDeInit(RTC_HandleTypeDef* hrtc)
{
  if(hrtc->Instance==RTC)
  {
  /* USER CODE BEGIN RTC_MspDeInit 0 */

  /* USER CODE END RTC_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_RTC_DISABLE();
  /* USER CODE BEGIN RTC_MspDeInit 1 */

  /* USER CODE END RTC_MspDeInit 1 */
  }

}
