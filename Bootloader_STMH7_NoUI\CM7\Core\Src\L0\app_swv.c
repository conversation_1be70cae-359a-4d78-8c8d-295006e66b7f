/****************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : app_swv.c
* @version 		  : 1.0.0
* @brief Functions for initializing and accessing serial wire viewer
* @details This declaration remaps the printf command to the serial wire viewer.
*          To enable, go Debug configurations and select the session to view.
*          Press the Debugger tab.
*          Under Serial Wire Viewer (SWV) check Enable.
*          Set the core clock to the system clock speed (480 MHz)
*          Set the SWO Clock to 2000.
*          Execute launch group for the desired chip.
*          Under the debug window, select the core you would like to view.
*          Navigate to Window/Show View/SWV/SWV ITM Data Console.
*          In the resulting window, press the settings tab.
*          Check the box under ITM Stimulus Ports for port 0.
*          Exit the page and press the record button in the Data Console.
*          Resume execution of the M4 processor, then the M7 processor.
*          https://www.youtube.com/watch?v=sPzQ5CniWtw
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include "app.h"

/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/

/******************************************************************************
* Module Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variable Definitions
*******************************************************************************/

/******************************************************************************
* Module Variable Definitions
*******************************************************************************/

/******************************************************************************
* Function Prototypes
*******************************************************************************/

/******************************************************************************
* Function Definitions
*******************************************************************************/
#if ENABLE_DEBUGGING_SWV
/* @fn int _write(int file, char *ptr, int len)
 * @brief Function remapping the printf to the serial wire viewer console. https://www.youtube.com/watch?v=sPzQ5CniWtw
 * @param None
 * @return None
 */
int _write(int file, char *ptr, int len)
{
  /* Implement your write code here, this is used by puts and printf for example */
  int i=0;
  for(i=0 ; i<len ; i++)
    ITM_SendChar((*ptr++));
  return len;
}
#endif
