/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_local_inputs.c
* @version 		  : 1.0.0
* @brief          : local inputs subroutine
* @details		  : local inputs subroutine
********************************************************************************/

/*******************************************************************************
* Includes
********************************************************************************/
#include "app.h"
#include "event_queue.h"
#include "sr_bootloader.h"

/*******************************************************************************
* Function Prototypes
********************************************************************************/
static void Init(void);
static void Run(void);

/*******************************************************************************
* Preprocessor Constants
********************************************************************************/
#define SUBROUTINE_FIRST_RUN_DELAY_1MS	  0
#define SUBROUTINE_RUN_INTERVAL_1MS				5
/*******************************************************************************
* Preprocessor Macros
********************************************************************************/
#define LOCAL_INPUT_DEBOUNCE_TIME__1MS			50

/*******************************************************************************
* Typedefs
********************************************************************************/


/*******************************************************************************
* Global Variable Definitions
********************************************************************************/
st_subroutine gstSR_Local_Inputs =
{
	.pfnInit = Init,
	.pfnRun = Run,
	.subroutine_name = "gstSR_Local_Inputs"
};

/*******************************************************************************
* Variable Definitions
********************************************************************************/
/* Local input states in bitmap form, where 1 is a given bit position
 * means the corresponding input is active. This is a debounced form of the inputs */
static uint32_t auiInputBitmap[BITMAP32_ARRAY_SIZE(NUM_LOCAL_INPUTS)];
static uint8_t aucDebounceCounter_1ms[NUM_LOCAL_INPUTS];

/*******************************************************************************
* Function Definitions
********************************************************************************/
/**
  * @fn  uint32_t LocalInputs_GetInputBitmap(uint8_t ucArrayIndex)
  * @brief Returns the status of inputs in bitmap form
  * @param ucArrayIndex, array index
  * @return Returns the status of inputs in bitmap form
  */
uint32_t LocalInputs_GetInputBitmap(uint8_t ucArrayIndex)
{
	return auiInputBitmap[ucArrayIndex];
}
/**
  * @fn  uint8_t LocalInputs_GetInputValue(en_local_inputs eInput)
  * @brief Returns the status of a particular input
  * @param eInput, the local input index
  * @return Returns 1 if the input is active, 0 otherwise
  */
uint8_t LocalInputs_GetInputValue(en_local_inputs eInput)
{
	return System_GetBitByIndex(&auiInputBitmap[0], eInput);
}
/**
  * @fn  static void LocalInputs_UpdateDebouncedInputs(void)
  * @brief Takes the local input commands and updates the corresponding pin states
  * @param None
  * @return None
  */
static void LocalInputs_UpdateDebouncedInputs(void)
{
	/* Checks input pin states and updates the debounced input status flags for each input */
	for(en_local_inputs eInput = 0; eInput < NUM_LOCAL_INPUTS; eInput++)
	{
		uint8_t bLastActive = System_GetBitByIndex(&auiInputBitmap[0], eInput);
		uint8_t bActive = GPIO_ReadInput(eInput);
		if(bLastActive != bActive)
		{
			if(aucDebounceCounter_1ms[eInput] < LOCAL_INPUT_DEBOUNCE_TIME__1MS)
			{
				aucDebounceCounter_1ms[eInput] += SUBROUTINE_RUN_INTERVAL_1MS;
			}
			else
			{
				System_SetBitByIndex(&auiInputBitmap[0], eInput, bActive);
				aucDebounceCounter_1ms[eInput] = 0;
			}
		}
		else
		{
			aucDebounceCounter_1ms[eInput] = 0;
		}
	}
}
/**
  * @fn  Init(void)
  * @brief Initializes local inputs subroutine first run delay, run interval, and variables
  * @param None
  * @return None
  */
static void Init(void)
{
	gstSR_Local_Inputs.uwFirstRunDelay_1ms = SUBROUTINE_FIRST_RUN_DELAY_1MS;
	gstSR_Local_Inputs.uwRunInterval_1ms = SUBROUTINE_RUN_INTERVAL_1MS;
}
/**
  * @fn  Run(void)
  * @brief local inputs subroutine run function executed every uwRunInterval_1ms
  * @param None
  * @return None
  */

static void Run(void)
{
	LocalInputs_UpdateDebouncedInputs();
}
