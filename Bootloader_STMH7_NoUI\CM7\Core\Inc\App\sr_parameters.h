/****************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file sr_parameters.h
 * @version 1.0.0
* @brief Subroutine updating processor's fault status
* @details Subroutine updating processor's fault status
*****************************************************************************/
#ifndef _SR_PARAMETERS_
#define _SR_PARAMETERS_

/******************************************************************************
* Includes
*******************************************************************************/
#include <stdint.h>
#include "app.h"
#include "scheduler.h"
#include "system.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/


/******************************************************************************
* Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variables
*******************************************************************************/
extern st_subroutine gstSR_Parameters;
/******************************************************************************
* Function Prototypes
*******************************************************************************/

#endif /* _SR_PARAMETERS_ */
